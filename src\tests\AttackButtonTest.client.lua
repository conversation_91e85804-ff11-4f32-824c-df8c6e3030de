--[[
	AttackButtonTest - 攻擊按鈕功能測試
	測試攻擊按鈕是否能正常工作
]]

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

local player = Players.LocalPlayer

-- 等待 Knit 啟動
Knit.OnStart():andThen(function()
	print("🧪 AttackButtonTest: Knit started")
	
	-- 等待一下讓所有控制器初始化
	task.wait(2)
	
	-- 獲取 CombatController
	local CombatController = Knit.GetController("CombatController")
	if not CombatController then
		warn("❌ CombatController not found")
		return
	end
	
	-- 獲取 UIManager
	local UIManager = Knit.GetController("UIManager")
	if not UIManager then
		warn("❌ UIManager not found")
		return
	end
	
	print("✅ Controllers found")
	
	-- 測試攻擊按鈕功能
	local function testAttackButton()
		print("🧪 Testing attack button...")
		
		-- 檢查是否有攻擊按鈕
		local playerGui = player:WaitForChild("PlayerGui")
		local combatUI = playerGui:FindFirstChild("CombatUI")
		
		if not combatUI then
			warn("❌ CombatUI not found")
			return
		end
		
		local attackButton = combatUI:FindFirstChild("ButtonContainer") and 
							combatUI.ButtonContainer:FindFirstChild("AttackButtonContainer") and
							combatUI.ButtonContainer.AttackButtonContainer:FindFirstChild("AttackButton")
		
		if not attackButton then
			warn("❌ Attack button not found")
			return
		end
		
		print("✅ Attack button found:", attackButton.Name)
		
		-- 測試直接調用攻擊方法
		print("🧪 Testing direct attack method call...")
		CombatController:PerformAttack()
		
		-- 模擬按鈕點擊
		print("🧪 Testing button click simulation...")
		task.wait(1)
		
		-- 檢查按鈕是否可點擊
		if attackButton.Active then
			print("✅ Attack button is active")
			-- 觸發按鈕點擊事件
			for _, connection in pairs(getconnections(attackButton.Activated)) do
				connection:Fire()
			end
		else
			print("⚠️ Attack button is not active")
		end
	end
	
	-- 測試怪物檢測
	local function testMonsterDetection()
		print("🧪 Testing monster detection...")
		
		-- 檢查場景中的怪物
		local monsterCount = 0
		for _, model in pairs(workspace:GetChildren()) do
			if model:IsA("Model") and model:FindFirstChild("MonsterId") then
				monsterCount = monsterCount + 1
				print("🔍 Found monster:", model.Name)
			end
		end
		
		print("🔍 Total monsters found:", monsterCount)
		
		if monsterCount == 0 then
			print("⚠️ No monsters found, spawning test monster...")
			-- 嘗試生成怪物
			local MonsterService = Knit.GetService("MonsterService")
			if MonsterService then
				MonsterService.SpawnMonster:Fire("goblin", Vector3.new(10, 0, 10))
				task.wait(2)
				testMonsterDetection() -- 重新檢查
			end
		end
	end
	
	-- 測試戰鬥服務連接
	local function testCombatService()
		print("🧪 Testing CombatService connection...")
		
		local CombatService = Knit.GetService("CombatService")
		if not CombatService then
			warn("❌ CombatService not found")
			return
		end
		
		print("✅ CombatService found")
		
		-- 檢查攻擊信號
		if CombatService.AttackMonster then
			print("✅ AttackMonster signal found")
		else
			warn("❌ AttackMonster signal not found")
		end
	end
	
	-- 設置測試按鍵
	local function setupTestKeys()
		print("🧪 Setting up test keys...")
		print("📝 Test Controls:")
		print("  F1 - Test attack button")
		print("  F2 - Test monster detection")
		print("  F3 - Test combat service")
		print("  F4 - Manual attack test")
		
		UserInputService.InputBegan:Connect(function(input, gameProcessed)
			if gameProcessed then return end
			
			if input.KeyCode == Enum.KeyCode.F1 then
				testAttackButton()
			elseif input.KeyCode == Enum.KeyCode.F2 then
				testMonsterDetection()
			elseif input.KeyCode == Enum.KeyCode.F3 then
				testCombatService()
			elseif input.KeyCode == Enum.KeyCode.F4 then
				print("🧪 Manual attack test...")
				CombatController:PerformAttack()
			end
		end)
	end
	
	-- 自動運行測試
	local function runAutoTests()
		print("🧪 Running automatic tests...")
		
		task.wait(1)
		testCombatService()
		
		task.wait(1)
		testMonsterDetection()
		
		task.wait(1)
		testAttackButton()
		
		print("🧪 Automatic tests completed")
	end
	
	-- 啟動測試
	setupTestKeys()
	runAutoTests()
	
end):catch(function(err)
	warn("❌ AttackButtonTest failed:", err)
end)
