--[[
	MonsterSpawnService - 怪物生成服務
	負責在戰鬥區域生成和管理怪物
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local RunService = game:GetService("RunService")

local MonsterSpawnService = Knit.CreateService({
	Name = "MonsterSpawnService",
	Client = {
		-- 客戶端請求手動生成怪物
		ForceSpawnMonster = Knit.CreateSignal(),
	},
})

-- 怪物配置
local MONSTER_CONFIGS = {
	goblin = {
		name = "哥布林",
		health = 50,
		attack = 15,
		speed = 12,
		experienceReward = 10,
		coinReward = 5,
		spawnWeight = 50,
	},
	orc = {
		name = "獸人",
		health = 80,
		attack = 25,
		speed = 10,
		experienceReward = 20,
		coinReward = 10,
		spawnWeight = 30,
	},
	skeleton = {
		name = "骷髏戰士",
		health = 60,
		attack = 20,
		speed = 14,
		experienceReward = 15,
		coinReward = 8,
		spawnWeight = 40,
	},
	dragon = {
		name = "幼龍",
		health = 200,
		attack = 50,
		speed = 16,
		experienceReward = 100,
		coinReward = 50,
		spawnWeight = 5,
	},
}

-- 生成點配置
local SPAWN_POINTS = {
	{position = Vector3.new(10, 0, 10), radius = 20, maxMonsters = 3},
	{position = Vector3.new(-10, 0, 15), radius = 15, maxMonsters = 2},
	{position = Vector3.new(15, 0, -10), radius = 15, maxMonsters = 2},
	{position = Vector3.new(-15, 0, -15), radius = 15, maxMonsters = 2},
	{position = Vector3.new(110, 0, 110), radius = 15, maxMonsters = 4},
	{position = Vector3.new(210, 0, 10), radius = 25, maxMonsters = 8}, -- Boss 區域
}

-- 私有變量
local spawnedMonsters = {} -- {[instanceId] = {config, spawnPoint, spawnTime}}
local lastSpawnCheck = 0
local SPAWN_CHECK_INTERVAL = 5 -- 每5秒檢查一次生成

function MonsterSpawnService:KnitStart()
	print("👹 MonsterSpawnService started")

	-- 處理客戶端手動生成請求
	self.Client.ForceSpawnMonster:Connect(function(player, position)
		self:_forceSpawnMonster(player, position)
	end)

	-- 開始生成循環
	RunService.Heartbeat:Connect(function()
		self:_updateSpawning()
	end)

	-- 延遲生成測試怪物（不阻塞主線程）
	task.spawn(function()
		print("🕐 Waiting 5 seconds before spawning test monsters...")
		task.wait(5) -- 等待玩家加入
		print("🕐 5 seconds passed, attempting to spawn test monsters...")
		self:_spawnTestMonsters()
	end)
end

-- 生成測試怪物
function MonsterSpawnService:_spawnTestMonsters()
	print("👹 Spawning test monsters...")

	-- 檢查 EntityService 是否可用
	if not self.EntityService then
		warn("❌ EntityService not available for monster spawning")
		return
	end

	-- 在玩家附近生成幾個怪物
	local testPositions = {
		Vector3.new(5, 0, 5),
		Vector3.new(-5, 0, 8),
		Vector3.new(8, 0, -5),
	}

	print("🔧 Starting to spawn", #testPositions, "test monsters")

	for i, position in ipairs(testPositions) do
		local monsterId = "goblin" -- 使用哥布林作為測試怪物
		local level = 1

		-- 創建怪物實體（獲取正確的 instanceId）
		local entityId, instanceId = self.EntityService:CreateMonsterEntity(monsterId, position, level)

		if entityId and instanceId then
			print("✅ Created monster entity:", entityId, "with instanceId:", instanceId)

			-- 創建怪物模型
			local monsterModel = self:_createMonsterModel(instanceId, monsterId, position, level)

			if monsterModel then
				-- 記錄生成的怪物
				spawnedMonsters[instanceId] = {
					config = MONSTER_CONFIGS[monsterId],
					spawnPointIndex = 1,
					spawnTime = tick(),
					entityId = entityId,
					model = monsterModel,
					monsterId = monsterId,
					currentHealth = MONSTER_CONFIGS[monsterId].health,
				}

				print("👹 Test monster spawned:", monsterId, "at", position, "instanceId:", instanceId)
			else
				warn("❌ Failed to create monster model for:", instanceId)
			end
		else
			warn("❌ Failed to create monster entity")
		end
	end

	print("👹 Test monsters spawning completed")
end

-- 強制生成怪物（客戶端請求）
function MonsterSpawnService:_forceSpawnMonster(player, position)
	print("🔥 Force spawning monster for", player.Name, "at", position)

	if not self.EntityService then
		warn("❌ EntityService not available")
		return
	end

	local monsterId = "goblin"
	local level = 1

	-- 如果沒有指定位置，在玩家附近生成
	if not position and player.Character and player.Character.PrimaryPart then
		local playerPos = player.Character.PrimaryPart.Position
		position = playerPos + Vector3.new(math.random(-10, 10), 0, math.random(-10, 10))
	end

	if not position then
		warn("❌ No valid position for monster spawn")
		return
	end

	-- 創建怪物實體
	local entityId, instanceId = self.EntityService:CreateMonsterEntity(monsterId, position, level)

	if entityId and instanceId then
		print("✅ Force created monster entity:", entityId, "with instanceId:", instanceId)

		-- 創建怪物模型
		local monsterModel = self:_createMonsterModel(instanceId, monsterId, position, level)

		if monsterModel then
			-- 更新實體的 Render 組件
			local world = _G.ECS_WORLD
			if world then
				local Components = require(game:GetService("ReplicatedStorage").Shared.ECS.Components)
				local primaryPart = monsterModel:FindFirstChild("HumanoidRootPart")

				if primaryPart then
					world:insert(entityId, Components.Render({
						model = monsterModel,
						primaryPart = primaryPart,
						animations = {},
					}))
					print("✅ Updated Render component for entity:", entityId, "model:", monsterModel.Name)
				else
					warn("❌ No HumanoidRootPart found for monster:", monsterModel.Name)
				end
			else
				warn("❌ ECS World not found when updating Render component")
			end

			-- 記錄生成的怪物
			spawnedMonsters[instanceId] = {
				config = MONSTER_CONFIGS[monsterId],
				spawnPointIndex = 0, -- 手動生成的怪物
				spawnTime = tick(),
				entityId = entityId,
				model = monsterModel,
				monsterId = monsterId,
				currentHealth = MONSTER_CONFIGS[monsterId].health,
			}

			print("👹 Force spawned monster:", monsterId, "at", position, "instanceId:", instanceId)
		else
			warn("❌ Failed to create monster model for:", instanceId)
		end
	else
		warn("❌ Failed to create monster entity")
	end
end

function MonsterSpawnService:KnitInit()
	-- 獲取其他服務
	self.EntityService = Knit.GetService("EntityService")
	self.ZoneService = Knit.GetService("ZoneService")
end

-- 更新生成邏輯
function MonsterSpawnService:_updateSpawning()
	local currentTime = tick()
	
	-- 檢查是否需要生成怪物
	if currentTime - lastSpawnCheck >= SPAWN_CHECK_INTERVAL then
		lastSpawnCheck = currentTime
		
		for i, spawnPoint in ipairs(SPAWN_POINTS) do
			self:_checkSpawnPoint(spawnPoint, i)
		end
	end
	
	-- 清理死亡的怪物
	self:_cleanupDeadMonsters()
end

-- 檢查生成點
function MonsterSpawnService:_checkSpawnPoint(spawnPoint, spawnPointIndex)
	-- 計算當前生成點的怪物數量
	local currentCount = 0
	for instanceId, monsterInfo in pairs(spawnedMonsters) do
		if monsterInfo.spawnPointIndex == spawnPointIndex then
			currentCount = currentCount + 1
		end
	end
	
	-- 如果未達到最大數量，嘗試生成
	if currentCount < spawnPoint.maxMonsters then
		-- 檢查是否有玩家在附近
		if self:_hasPlayersNearby(spawnPoint.position, spawnPoint.radius + 10) then
			self:_spawnMonster(spawnPoint, spawnPointIndex)
		end
	end
end

-- 檢查附近是否有玩家
function MonsterSpawnService:_hasPlayersNearby(position, radius)
	local players = game.Players:GetPlayers()

	for _, player in pairs(players) do
		if player.Character and player.Character.PrimaryPart then
			local distance = (player.Character.PrimaryPart.Position - position).Magnitude
			print("🔍 Player", player.Name, "distance from spawn point:", distance, "radius:", radius)
			if distance <= radius then
				print("✅ Player nearby, can spawn monsters")
				return true
			end
		end
	end

	print("❌ No players nearby for spawning")
	return false
end

-- 生成怪物
function MonsterSpawnService:_spawnMonster(spawnPoint, spawnPointIndex)
	-- 選擇怪物類型
	local monsterId = self:_selectMonsterType(spawnPointIndex)
	local config = MONSTER_CONFIGS[monsterId]
	
	if not config then
		warn("❌ Monster config not found:", monsterId)
		return
	end
	
	-- 計算生成位置（在生成點半徑內隨機）
	local angle = math.random() * math.pi * 2
	local distance = math.random() * spawnPoint.radius
	local spawnPosition = spawnPoint.position + Vector3.new(
		math.cos(angle) * distance,
		0,
		math.sin(angle) * distance
	)
	
	-- 計算怪物等級（基於生成點）
	local level = self:_calculateMonsterLevel(spawnPointIndex)
	
	-- 根據等級調整屬性
	local scaledHealth = math.floor(config.health * (1 + (level - 1) * 0.2))
	local scaledAttack = math.floor(config.attack * (1 + (level - 1) * 0.15))
	
	-- 創建怪物實體
	local monsterEntity, instanceId = self.EntityService:CreateMonsterEntity(
		monsterId,
		spawnPosition,
		level,
		scaledHealth,
		scaledAttack
	)
	
	if monsterEntity and instanceId then
		-- 記錄生成的怪物
		spawnedMonsters[instanceId] = {
			monsterId = monsterId,
			config = config,
			spawnPointIndex = spawnPointIndex,
			spawnTime = tick(),
			level = level,
			entityId = monsterEntity,
		}
		
		-- 創建怪物模型
		self:_createMonsterModel(instanceId, monsterId, spawnPosition, level)
		
		print(string.format("👹 Spawned %s (Level %d) at %s", 
			config.name, level, tostring(spawnPosition)))
	end
end

-- 選擇怪物類型
function MonsterSpawnService:_selectMonsterType(spawnPointIndex)
	-- Boss 區域有特殊邏輯
	if spawnPointIndex == 3 then -- Boss 區域
		-- 更高機率生成強力怪物
		local rand = math.random(100)
		if rand <= 20 then
			return "dragon"
		elseif rand <= 50 then
			return "orc"
		else
			return "skeleton"
		end
	end
	
	-- 普通區域使用權重隨機
	local totalWeight = 0
	for _, config in pairs(MONSTER_CONFIGS) do
		totalWeight = totalWeight + config.spawnWeight
	end
	
	local randomValue = math.random() * totalWeight
	local currentWeight = 0
	
	for monsterId, config in pairs(MONSTER_CONFIGS) do
		currentWeight = currentWeight + config.spawnWeight
		if randomValue <= currentWeight then
			return monsterId
		end
	end
	
	-- 備用方案
	return "goblin"
end

-- 計算怪物等級
function MonsterSpawnService:_calculateMonsterLevel(spawnPointIndex)
	if spawnPointIndex == 1 then
		return math.random(1, 3) -- 新手區域
	elseif spawnPointIndex == 2 then
		return math.random(3, 6) -- 中級區域
	elseif spawnPointIndex == 3 then
		return math.random(8, 12) -- Boss 區域
	end
	
	return 1
end

-- 創建怪物模型
function MonsterSpawnService:_createMonsterModel(instanceId, monsterId, position, level)
	-- 創建簡單的怪物模型
	local model = Instance.new("Model")
	model.Name = monsterId .. "_" .. instanceId  -- 正確的命名格式

	-- 添加 MonsterId 標識（TargetingSystem 需要）
	local monsterIdValue = Instance.new("StringValue")
	monsterIdValue.Name = "MonsterId"
	monsterIdValue.Value = monsterId
	monsterIdValue.Parent = model

	-- 創建 HumanoidRootPart（TargetingSystem 需要）
	local rootPart = Instance.new("Part")
	rootPart.Name = "HumanoidRootPart"
	rootPart.Size = Vector3.new(2, 0.1, 1)
	rootPart.Position = position
	rootPart.Anchored = true
	rootPart.Transparency = 1
	rootPart.CanCollide = false
	rootPart.Parent = model

	-- 主體
	local body = Instance.new("Part")
	body.Name = "Body"
	body.Size = Vector3.new(2, 3, 1)
	body.Position = position + Vector3.new(0, 1.5, 0)
	body.Anchored = true
	body.BrickColor = BrickColor.new("Really red")
	body.Parent = model

	-- 設置為主要部件
	model.PrimaryPart = rootPart
	
	-- 添加人形
	local humanoid = Instance.new("Humanoid")
	humanoid.MaxHealth = MONSTER_CONFIGS[monsterId].health
	humanoid.Health = humanoid.MaxHealth
	humanoid.Parent = model
	
	-- 添加標籤顯示
	local gui = Instance.new("BillboardGui")
	gui.Name = "HealthDisplay"
	gui.Size = UDim2.new(0, 200, 0, 60)
	gui.StudsOffset = Vector3.new(0, 3, 0)
	gui.Parent = body

	-- 怪物名稱和等級
	local nameLabel = Instance.new("TextLabel")
	nameLabel.Name = "NameLabel"
	nameLabel.Size = UDim2.new(1, 0, 0.5, 0)
	nameLabel.Position = UDim2.new(0, 0, 0, 0)
	nameLabel.BackgroundTransparency = 1
	nameLabel.Text = MONSTER_CONFIGS[monsterId].name .. " (Lv." .. level .. ")"
	nameLabel.TextColor3 = Color3.new(1, 1, 1)
	nameLabel.TextScaled = true
	nameLabel.Font = Enum.Font.GothamBold
	nameLabel.TextStrokeTransparency = 0
	nameLabel.TextStrokeColor3 = Color3.new(0, 0, 0)
	nameLabel.Parent = gui

	-- 血條背景
	local healthBarBg = Instance.new("Frame")
	healthBarBg.Name = "HealthBarBackground"
	healthBarBg.Size = UDim2.new(0.9, 0, 0.3, 0)
	healthBarBg.Position = UDim2.new(0.05, 0, 0.6, 0)
	healthBarBg.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
	healthBarBg.BorderSizePixel = 1
	healthBarBg.BorderColor3 = Color3.new(0, 0, 0)
	healthBarBg.Parent = gui

	-- 血條填充
	local healthBar = Instance.new("Frame")
	healthBar.Name = "HealthBar"
	healthBar.Size = UDim2.new(1, 0, 1, 0)
	healthBar.Position = UDim2.new(0, 0, 0, 0)
	healthBar.BackgroundColor3 = Color3.new(0, 1, 0) -- 綠色
	healthBar.BorderSizePixel = 0
	healthBar.Parent = healthBarBg

	-- 血量文字
	local healthText = Instance.new("TextLabel")
	healthText.Name = "HealthText"
	healthText.Size = UDim2.new(1, 0, 1, 0)
	healthText.Position = UDim2.new(0, 0, 0, 0)
	healthText.BackgroundTransparency = 1
	healthText.Text = humanoid.Health .. "/" .. humanoid.MaxHealth
	healthText.TextColor3 = Color3.new(1, 1, 1)
	healthText.TextScaled = true
	healthText.Font = Enum.Font.Gotham
	healthText.TextStrokeTransparency = 0
	healthText.TextStrokeColor3 = Color3.new(0, 0, 0)
	healthText.Parent = healthBarBg
	
	-- 存儲實例 ID
	model:SetAttribute("InstanceId", instanceId)
	
	-- 放置到工作區
	model.Parent = workspace
	
	return model
end

-- 清理死亡的怪物
function MonsterSpawnService:_cleanupDeadMonsters()
	local toRemove = {}
	
	for instanceId, monsterInfo in pairs(spawnedMonsters) do
		-- 檢查實體是否還存在
		local entityId = self.EntityService:GetMonsterEntityId(instanceId)
		if not entityId then
			table.insert(toRemove, instanceId)
		else
			-- 檢查是否超時（10分鐘無人攻擊自動消失）
			local currentTime = tick()
			if currentTime - monsterInfo.spawnTime > 600 then
				self:_despawnMonster(instanceId)
				table.insert(toRemove, instanceId)
			end
		end
	end
	
	-- 清理記錄
	for _, instanceId in ipairs(toRemove) do
		spawnedMonsters[instanceId] = nil
	end
end

-- 手動消除怪物
function MonsterSpawnService:_despawnMonster(instanceId)
	-- 銷毀實體
	self.EntityService:DestroyMonsterEntity(instanceId)
	
	-- 銷毀模型
	local model = workspace:FindFirstChild(function(child)
		return child:GetAttribute("InstanceId") == instanceId
	end)
	
	if model then
		model:Destroy()
	end
	
	print(string.format("👹 Despawned monster %s", instanceId))
end

-- 獲取怪物信息
function MonsterSpawnService:GetMonsterInfo(instanceId)
	return spawnedMonsters[instanceId]
end

-- 獲取所有生成的怪物
function MonsterSpawnService:GetAllSpawnedMonsters()
	return spawnedMonsters
end

-- 手動生成怪物（用於測試）
function MonsterSpawnService:ForceSpawnMonster(monsterId, position, level)
	local config = MONSTER_CONFIGS[monsterId]
	if not config then
		warn("❌ Monster config not found:", monsterId)
		return nil
	end
	
	level = level or 1
	local scaledHealth = math.floor(config.health * (1 + (level - 1) * 0.2))
	local scaledAttack = math.floor(config.attack * (1 + (level - 1) * 0.15))
	
	local monsterEntity, instanceId = self.EntityService:CreateMonsterEntity(
		monsterId,
		position,
		level,
		scaledHealth,
		scaledAttack
	)
	
	if monsterEntity and instanceId then
		spawnedMonsters[instanceId] = {
			monsterId = monsterId,
			config = config,
			spawnPointIndex = 0, -- 手動生成
			spawnTime = tick(),
			level = level,
			entityId = monsterEntity,
		}
		
		self:_createMonsterModel(instanceId, monsterId, position, level)
		
		print(string.format("👹 Force spawned %s (Level %d) at %s", 
			config.name, level, tostring(position)))
		
		return instanceId
	end
	
	return nil
end

return MonsterSpawnService
