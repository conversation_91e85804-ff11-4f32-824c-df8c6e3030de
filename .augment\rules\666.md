---
type: "always_apply"
---

架構原則
1. 分層清晰：所有功能劃分為三大層級：
Knit（資料/邏輯服務）
Matter ECS（實體邏輯）
Fusion UI（前端顯示）
2. 資料不可於客戶端修改：
抽卡結果、金幣扣除、寵物解鎖等需經由 Server（Knit Service）控制
客戶端只作 UI 與動畫展示，不可擁有權限變更 ProfileService 資料
3. 模組化、可擴充性：
每種功能（戰鬥、寵物、扭蛋）獨立模組
Entity 組件不允許出現跨邏輯耦合（例如 PetComponent 不應直接依賴 WeaponComponent）

🔧 開發注意事項
1. ProfileService 一律由 Server 初始化與保存
每位玩家登入時建立資料快照，不在 Client 操作 OwnedPets 或 Coins
2. Signal 用於高頻事件（如：寵物攻擊、技能觸發）
避免用 RemoteEvent 連續傳送（延遲 + 資源消耗）
3. Knit Controller 不應包含資料邏輯，只負責 UI 與輸入綁定
4. ECS 組件必須小型、純資料，系統才能處理所有邏輯
❌ 組件中不能含有函數
✅ 組件只儲存狀態（ex: hp, moveSpeed, targetId）

5. Fusion UI 必須用 mount/unmount 控制
避免所有 UI 永遠載入在場景上，會影響效能

🚫 禁止與排除規範

1. ❌ 不要使用 Humanoid:MoveTo() 控制角色或寵物移動
請使用 BodyVelocity 或自製移動系統（ECS 控制）
2. ❌ 不要在 Controller 中直接操作 Workspace 物件屬性
若需生成模型或控制移動，請呼叫 Knit Service or ECS 插入實體
3. ❌ 不要將資料存放在 ReplicatedStorage（例如：寵物清單）
正確作法：用 Knit Service 提供給 Client 存取，或經由 Knit remote 呼叫
4. ❌ 不要將攻擊判定寫在 Client 端（揮劍 / 技能）
須經由 Server 檢查再觸發 ECS 系統處理
5. ❌ 不要讓客戶端傳送 Damage 數值
Client 傳送『我要攻擊誰』，Server 計算命中與傷害

🔄 整體流程邏輯（範例：抽卡 → 裝備 → 攻擊）

1. 玩家開啟扭蛋 UI（Fusion UI + Knit Controller）
2. 玩家點擊抽卡按鈕 → 呼叫 GachaService:Roll()
3. Server 檢查金幣、隨機選寵物，寫入 ProfileService → 回傳結果
4. Client 顯示抽卡動畫與寵物模型
5. 若選擇裝備 → 呼叫 PetService:Equip() 更新 EquippedPets
6. PetService 建立 ECS Entity → 加入 PetComponent, FollowComponent
7. Matter 系統開始處理寵物跟隨 / 攻擊行為


✅ 測試與除錯
使用 TestEZ 撰寫單元測試（至少覆蓋 GachaService / PetService）
啟用 Matter DebugView 查看 Entity 狀態
UI Debug 模式需能顯示：
擁有的寵物 ID
已裝備寵物 ID
血量/目標等 ECS 組件資料

📁 專案結構建議

Knit 架構層（模組化服務與控制器）
│
├── 🔹 PlayerService         ⬅ 玩家登入 / 資料載入 / ProfileService 綁定
├── 🔹 PetService            ⬅ 寵物裝備 / 升級 / 存取圖鑑資料
├── 🔹 WeaponService         ⬅ 裝備武器 / 揮劍特效觸發
├── 🔹 CombatService         ⬅ 揮劍指令發送 / 呼叫 ECS 攻擊處理
├── 🔹 GachaService          ⬅ 抽卡邏輯、機率選擇、金幣扣除
├── 🔹 ZoneService           ⬅ 偵測進入戰鬥區 / 安全區（結合 ZonePlus）
│
├── 🔸 PetController         ⬅ 前端控制寵物圖鑑、UI 顯示
├── 🔸 CombatController      ⬅ 玩家輸入：攻擊鍵 / 使用技能
├── 🔸 GachaController       ⬅ 抽卡 UI、動畫、展示結果
└── 🔸 UIController          ⬅ 統一 UI 控制（切換頁面、提示）

⚙️ Matter ECS 實體系統層（行為邏輯、AI、移動、攻擊）
│
├── 🧩 Entity：PlayerEntity, PetEntity, MonsterEntity
│
├── 📍 Components
│   ├── PositionComponent
│   ├── HealthComponent
│   ├── DamageComponent
│   ├── PetComponent
│   ├── FollowTargetComponent
│   ├── SwordSwingComponent
│   └── TargetComponent
│
└── 🔁 Systems
    ├── SwordAttackSystem      ⬅ 判定揮劍命中範圍，發出傷害
    ├── PetFollowSystem        ⬅ 寵物追蹤主人
    ├── PetAttackSystem        ⬅ 寵物自動攻擊目標
    ├── HealthSystem           ⬅ 處理傷害、死亡
    ├── MonsterAISystem        ⬅ 簡單怪物 AI：巡邏 / 追擊 / 攻擊
    └── LifetimeSystem         ⬅ 寵物存活時間控制（如召喚獸）

🧩 模型 / 靜態資料模組（ModuleScripts）
│
├── PetDatabase               ⬅ 所有寵物資料與抽卡機率
├── WeaponDatabase            ⬅ 武器名稱、傷害、特效綁定
├── GachaPoolManager          ⬅ 抽卡池切換 / 活動專屬池
├── UIAssets                  ⬅ UI 元件樣式設定
└── Utility 模組             ⬅ TableUtil, Signal, Janitor, Promise

💾 資料結構（ProfileService 格式）
│
├── Coins (金幣)
├── OwnedPets = { [petId] = {level, exp, rarity} }
├── EquippedPets = { "Slime", "FireDragon" }
├── OwnedWeapons = { "BronzeSword", "MagicBlade" }
└── GachaHistory = { last10抽 }

🎨 視覺呈現（客戶端）
│
├── Fusion UI
│   ├── PetBookUI             ⬅ 寵物圖鑑
│   ├── GachaUI               ⬅ 抽卡彈窗動畫
│   ├── CombatHUD             ⬅ 血量條 / 技能按鈕
│   └── PopupToast            ⬅ 成就提示、抽中提示
│
├── Flipper 動畫              ⬅ UI 彈出 / 星級展示
├── Tween / Particle          ⬅ 揮劍 / 擊中特效
└── BillboardGui              ⬅ 血量條顯示在頭上

🧪 測試 / 調試工具
│
├── TestEZ 測試模組          ⬅ Knit Service 單元測試
├── Matter Debug View         ⬅ 檢視 ECS 系統內部狀態
└── DeveloperMode UI          ⬅ 顯示 Entity 狀態、碰撞框、數據


如需多人協作或進一步規模化，可再擴充：日誌系統、Plugin 開發工具、自動 UI 測試等.
