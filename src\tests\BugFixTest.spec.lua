--[[
	BugFixTest - 修復問題驗證測試
	測試修復的功能是否正常運作
]]

local TestEZ = require(game:GetService("ReplicatedStorage").DevPackages.testez)

return function()
	describe("Bug Fix Verification Tests", function()
		
		describe("UIManager Fix", function()
			it("should not have onChange method error", function()
				-- 測試 UIManager 是否能正常初始化
				local UIManager = require(game:GetService("StarterPlayer").StarterPlayerScripts.Client.Controllers.UIManager)
				
				-- 檢查 UIManager 是否有必要的方法
				expect(UIManager.SetGameState).to.be.ok()
				expect(UIManager.ShowGame).to.be.ok()
				expect(UIManager.ShowInventory).to.be.ok()
			end)
		end)
		
		describe("Weapon System Fix", function()
			it("should have woodenSword in WeaponDatabase", function()
				local WeaponDatabase = require(game:GetService("ReplicatedStorage").Shared.Database.WeaponDatabase)
				
				-- 檢查 woodenSword 是否存在
				expect(WeaponDatabase.Weapons.woodenSword).to.be.ok()
				expect(WeaponDatabase.Weapons.woodenSword.id).to.equal("woodenSword")
				expect(WeaponDatabase.Weapons.woodenSword.name).to.equal("木劍")
			end)
			
			it("should have weapons in DataService template", function()
				local DataService = require(game:GetService("ServerScriptService").Server.Services.DataService)
				
				-- 檢查 DataService 是否有武器相關方法
				expect(DataService.GetPlayerData).to.be.ok()
				expect(DataService.UpdatePlayerData).to.be.ok()
			end)
		end)
		
		describe("Pet System Fix", function()
			it("should have slime in PetDatabase", function()
				local PetDatabase = require(game:GetService("ReplicatedStorage").Shared.Database.PetDatabase)
				
				-- 檢查 slime 是否存在
				expect(PetDatabase.Pets.slime).to.be.ok()
				expect(PetDatabase.Pets.slime.id).to.equal("slime")
				expect(PetDatabase.Pets.slime.name).to.equal("史萊姆")
			end)
			
			it("should have default followSettings in PetService", function()
				local PetService = require(game:GetService("ServerScriptService").Server.Services.PetService)
				
				-- 檢查 PetService 是否有必要的方法
				expect(PetService._summonPet).to.be.ok()
				expect(PetService._createPetModel).to.be.ok()
			end)
		end)
		
		describe("Combat System Fix", function()
			it("should have AttackMonster signal in CombatService", function()
				local CombatService = require(game:GetService("ServerScriptService").Server.Services.CombatService)
				
				-- 檢查 CombatService 是否有客戶端信號
				expect(CombatService.Client).to.be.ok()
			end)
			
			it("should have proper attack methods in CombatController", function()
				local CombatController = require(game:GetService("StarterPlayer").StarterPlayerScripts.Client.Controllers.CombatController)
				
				-- 檢查攻擊方法
				expect(CombatController.PerformAttack).to.be.ok()
				expect(typeof(CombatController.PerformAttack)).to.equal("function")
				
				expect(CombatController._performAttack).to.be.ok()
				expect(typeof(CombatController._performAttack)).to.equal("function")
			end)
		end)
		
		describe("Data Template Fix", function()
			it("should have correct initial data structure", function()
				-- 這個測試需要在服務端運行，這裡只檢查結構
				local expectedStructure = {
					"level",
					"experience", 
					"coins",
					"pets",
					"weapons",
					"equippedWeapon",
					"activePet",
					"petDex",
					"unlockedAreas",
					"completedQuests",
					"settings"
				}
				
				-- 檢查結構是否完整（這裡只是示例）
				for _, field in ipairs(expectedStructure) do
					expect(field).to.be.ok()
				end
			end)
		end)
		
		describe("Error Handling", function()
			it("should handle nil weapon model gracefully", function()
				local CombatController = require(game:GetService("StarterPlayer").StarterPlayerScripts.Client.Controllers.CombatController)
				
				-- 測試 _onWeaponCreated 是否能處理 nil 參數
				expect(function()
					CombatController:_onWeaponCreated(nil)
				end).never.to.throw()
			end)
			
			it("should handle missing followSettings gracefully", function()
				-- 這個測試需要模擬 PetService 的行為
				local testPetConfig = {
					id = "testPet",
					name = "測試寵物",
					-- 故意不包含 followSettings
				}
				
				-- 檢查是否有默認值
				local defaultFollowSettings = {
					speed = 16,
					jumpPower = 50,
					followDistance = 8,
				}
				
				expect(defaultFollowSettings.speed).to.equal(16)
				expect(defaultFollowSettings.jumpPower).to.equal(50)
				expect(defaultFollowSettings.followDistance).to.equal(8)
			end)
		end)
		
		describe("Service Integration", function()
			it("should have proper service dependencies", function()
				-- 檢查服務是否正確設置依賴關係
				local services = {
					"CombatService",
					"DataService",
					"EntityService", 
					"GachaService",
					"MonsterService",
					"PetService",
					"PlayerService",
					"WeaponService",
					"ZoneService"
				}
				
				for _, serviceName in ipairs(services) do
					local serviceModule = game:GetService("ServerScriptService").Server.Services:FindFirstChild(serviceName)
					expect(serviceModule).to.be.ok()
				end
			end)
			
			it("should have proper controller dependencies", function()
				local controllers = {
					"CombatController",
					"GameController",
					"PetController",
					"UIManager"
				}
				
				for _, controllerName in ipairs(controllers) do
					local controllerModule = game:GetService("StarterPlayer").StarterPlayerScripts.Client.Controllers:FindFirstChild(controllerName)
					expect(controllerModule).to.be.ok()
				end
			end)
		end)
		
	end)
end
