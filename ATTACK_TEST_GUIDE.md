# ⚔️ 攻擊功能測試指南

## 🎯 當前狀況
根據最新日誌分析：

### ✅ **已正常工作的功能**
1. **UIManager**：正常啟動，無錯誤
2. **戰鬥 UI**：成功創建和掛載
3. **AttackTest 腳本**：正常運行，提供 F1-F5 測試按鍵
4. **CombatController**：正常初始化

### ❌ **需要解決的問題**
1. **沒有怪物**：場景中沒有怪物可以攻擊
2. **自動生成未觸發**：怪物自動生成系統沒有工作

## 🔧 **已實施的修復**

### 1. 添加自動測試怪物生成
```lua
-- 在 MonsterSpawnService 中添加立即生成測試怪物
function MonsterSpawnService:_spawnTestMonsters()
    local testPositions = {
        Vector3.new(5, 0, 5),
        Vector3.new(-5, 0, 8), 
        Vector3.new(8, 0, -5),
    }
    -- 在玩家附近生成 3 個哥布林
end
```

### 2. 增強調試信息
```lua
-- 在 _hasPlayersNearby 中添加距離檢查日誌
print("🔍 Player", player.Name, "distance from spawn point:", distance)
```

### 3. 自動測試流程
```lua
-- AttackTest 會自動運行：
-- 1. 檢查 UIManager
-- 2. 檢查戰鬥 UI
-- 3. 自動生成怪物
```

## 🧪 **測試步驟**

### **重新啟動遊戲後：**

#### 1. **等待自動生成**
- 遊戲啟動 5 秒後應該會自動生成 3 個測試怪物
- 觀察控制台是否出現：
  ```
  👹 Spawning test monsters...
  👹 Test monster spawned: goblin at Vector3(5, 0, 5)
  👹 Test monster spawned: goblin at Vector3(-5, 0, 8)
  👹 Test monster spawned: goblin at Vector3(8, 0, -5)
  👹 Test monsters spawning completed
  ```

#### 2. **手動測試按鍵**
- **F1** - 檢查戰鬥 UI 和攻擊按鈕
- **F2** - 手動生成更多怪物
- **F3** - 直接測試攻擊功能
- **F4** - 檢查 UIManager 狀態
- **F5** - 運行所有測試

#### 3. **測試攻擊功能**
一旦有怪物出現：
1. **走近怪物**（10格內）
2. **點擊攻擊按鈕**
3. **觀察攻擊效果**：
   - 劍光軌跡
   - 斬擊痕跡
   - 小爆炸效果

## 📊 **預期結果**

### **成功的攻擊應該顯示：**
```
🔥 Attack button clicked!
🔥 Attack not on cooldown, calling PerformAttack
🔥 PerformAttack called from UI
🔥 _performAttack called
🔍 Searching for monsters from position: Vector3(...)
🔍 Found monster: goblin_123456 at Vector3(...)
🔍 Total monsters in workspace: 3
🎯 Attacking monster: 123456
🎬 _performSwordSwing called
⚔️ Performing close-range sword attack, distance: 8.5
✨ Creating melee attack effects
✨ Melee sword trail effect created
⚔️ Melee attack effects completed!
```

### **距離太遠時應該顯示：**
```
🎯 Attacking monster: 123456
🎬 _performSwordSwing called
❌ Target too far for sword attack: 15.2
```

## 🔍 **故障排除**

### **如果沒有看到怪物生成：**
1. 檢查控制台是否有錯誤
2. 按 **F2** 手動生成怪物
3. 檢查 MonsterSpawnService 是否正常啟動

### **如果攻擊按鈕沒有反應：**
1. 按 **F1** 檢查 UI 狀態
2. 確認攻擊按鈕是否存在且 Active
3. 檢查是否有目標怪物

### **如果攻擊沒有效果：**
1. 確認距離是否在 10 格內
2. 檢查怪物是否有正確的 MonsterId
3. 按 **F3** 直接測試攻擊方法

## 🎯 **測試重點**

### **近距離劍砍邏輯：**
1. **距離限制**：只能攻擊 10 格內的目標
2. **視覺效果**：
   - 藍色劍光軌跡（0.1秒出現，0.3秒消失）
   - 目標位置斬擊痕跡
   - 小範圍爆炸（3格半徑）
3. **攻擊反饋**：
   - 距離太遠時顯示提示
   - 成功攻擊時播放特效

### **與之前的對比：**
- **修復前**：遠距離光束攻擊，大爆炸
- **修復後**：近距離劍砍，小爆炸，符合邏輯

## 📝 **測試清單**

- [ ] 遊戲啟動無錯誤
- [ ] 戰鬥 UI 正確顯示
- [ ] 攻擊按鈕可見且可點擊
- [ ] 怪物自動生成（或手動生成成功）
- [ ] 近距離攻擊成功（≤10格）
- [ ] 遠距離攻擊被阻止（>10格）
- [ ] 劍光軌跡效果正確
- [ ] 斬擊痕跡出現在目標位置
- [ ] 爆炸範圍合理（3格）

## 🚀 **下一步**

如果所有測試都通過：
1. **移除調試信息**：清理控制台輸出
2. **優化特效**：調整動畫時間和視覺效果
3. **添加音效**：劍砍和擊中音效
4. **實施傷害系統**：讓攻擊真正對怪物造成傷害

請重新啟動遊戲並按照這個指南進行測試，告訴我您看到的結果！
