--[[
	ArchitectureComplianceTest - 架構規則遵循度測試
	測試專案是否遵循 .augment/rules/666.md 中的架構原則
]]

local TestEZ = require(game:GetService("ReplicatedStorage").DevPackages.testez)

return function()
	describe("Architecture Compliance Tests", function()
		
		describe("ECS Components", function()
			it("should have pure data components without functions", function()
				local Components = require(game:GetService("ReplicatedStorage").Shared.ECS.Components)
				
				-- 檢查所有組件是否為純資料
				for componentName, component in pairs(Components) do
					-- 組件應該是 Matter 組件
					expect(typeof(component)).to.equal("table")
					
					-- 檢查組件是否有 Matter 的特徵
					expect(component._name).to.be.ok()
					expect(component._schema).to.be.ok()
					
					-- 檢查組件 schema 中沒有函數
					for fieldName, fieldValue in pairs(component._schema) do
						expect(typeof(fieldValue)).never.to.equal("function")
					end
				end
			end)
		end)
		
		describe("Service Layer", function()
			it("should have proper server services", function()
				local expectedServices = {
					"CombatService",
					"DataService", 
					"EntityService",
					"GachaService",
					"MonsterService",
					"PetService",
					"PlayerService",
					"WeaponService",
					"ZoneService"
				}
				
				for _, serviceName in ipairs(expectedServices) do
					local serviceModule = game:GetService("ServerScriptService").Server.Services:FindFirstChild(serviceName)
					expect(serviceModule).to.be.ok()
					expect(serviceModule.ClassName).to.equal("ModuleScript")
				end
			end)
			
			it("should have proper client controllers", function()
				local expectedControllers = {
					"CombatController",
					"GameController",
					"GameUIController", 
					"HealthBarController",
					"PetController",
					"PetFollowController",
					"PetUIController",
					"UIController",
					"UIManager"
				}
				
				for _, controllerName in ipairs(expectedControllers) do
					local controllerModule = game:GetService("StarterPlayer").StarterPlayerScripts.Client.Controllers:FindFirstChild(controllerName)
					expect(controllerModule).to.be.ok()
					expect(controllerModule.ClassName).to.equal("ModuleScript")
				end
			end)
		end)
		
		describe("Data Security", function()
			it("should not have game data in ReplicatedStorage", function()
				-- 檢查 ReplicatedStorage 中不應該有玩家資料
				local replicatedStorage = game:GetService("ReplicatedStorage")
				
				-- 允許的項目（配置資料）
				local allowedItems = {
					"Packages",
					"Shared"
				}
				
				for _, child in ipairs(replicatedStorage:GetChildren()) do
					local isAllowed = false
					for _, allowedItem in ipairs(allowedItems) do
						if child.Name == allowedItem then
							isAllowed = true
							break
						end
					end
					
					if not isAllowed then
						-- 檢查是否為配置資料
						expect(child.Name).never.to.match("PlayerData")
						expect(child.Name).never.to.match("Coins")
						expect(child.Name).never.to.match("Pets")
						expect(child.Name).never.to.match("Weapons")
					end
				end
			end)
		end)
		
		describe("Weapon System", function()
			it("should have weapon creation in WeaponService", function()
				local WeaponService = require(game:GetService("ServerScriptService").Server.Services.WeaponService)
				
				-- 檢查 WeaponService 是否有必要的方法
				expect(WeaponService._ensurePlayerWeapon).to.be.ok()
				expect(typeof(WeaponService._ensurePlayerWeapon)).to.equal("function")
				
				expect(WeaponService._createBasicSwordModel).to.be.ok()
				expect(typeof(WeaponService._createBasicSwordModel)).to.equal("function")
			end)
		end)
		
		describe("UI Management", function()
			it("should have UIManager for UI lifecycle", function()
				local UIManager = require(game:GetService("StarterPlayer").StarterPlayerScripts.Client.Controllers.UIManager)
				
				-- 檢查 UIManager 是否有必要的方法
				expect(UIManager.SetGameState).to.be.ok()
				expect(typeof(UIManager.SetGameState)).to.equal("function")
				
				expect(UIManager.ShowInventory).to.be.ok()
				expect(typeof(UIManager.ShowInventory)).to.equal("function")
				
				expect(UIManager.ShowGame).to.be.ok()
				expect(typeof(UIManager.ShowGame)).to.equal("function")
				
				expect(UIManager.RefreshAllUI).to.be.ok()
				expect(typeof(UIManager.RefreshAllUI)).to.equal("function")
			end)
			
			it("should have state access methods in CombatController", function()
				local CombatController = require(game:GetService("StarterPlayer").StarterPlayerScripts.Client.Controllers.CombatController)
				
				-- 檢查狀態訪問方法
				expect(CombatController.GetHealthState).to.be.ok()
				expect(typeof(CombatController.GetHealthState)).to.equal("function")
				
				expect(CombatController.GetMaxHealthState).to.be.ok()
				expect(typeof(CombatController.GetMaxHealthState)).to.equal("function")
				
				expect(CombatController.GetAttackCooldownState).to.be.ok()
				expect(typeof(CombatController.GetAttackCooldownState)).to.equal("function")
				
				expect(CombatController.GetIsAttackOnCooldown).to.be.ok()
				expect(typeof(CombatController.GetIsAttackOnCooldown)).to.equal("function")
			end)
		end)
		
		describe("ECS Systems", function()
			it("should have proper ECS systems", function()
				local expectedSystems = {
					"HealthSystem",
					"LifetimeSystem", 
					"MonsterAISystem",
					"PetAttackSystem",
					"PetFollowSystem",
					"SwordAttackSystem"
				}
				
				for _, systemName in ipairs(expectedSystems) do
					local systemModule = game:GetService("ServerScriptService").Server.Systems:FindFirstChild(systemName)
					expect(systemModule).to.be.ok()
					expect(systemModule.ClassName).to.equal("ModuleScript")
				end
			end)
		end)
		
		describe("Database Structure", function()
			it("should have proper database modules", function()
				local expectedDatabases = {
					"PetDatabase",
					"WeaponDatabase"
				}
				
				for _, dbName in ipairs(expectedDatabases) do
					local dbModule = game:GetService("ReplicatedStorage").Shared.Database:FindFirstChild(dbName)
					expect(dbModule).to.be.ok()
					expect(dbModule.ClassName).to.equal("ModuleScript")
				end
			end)
		end)
		
		describe("ProfileService Integration", function()
			it("should use ProfileService in DataService", function()
				local DataService = require(game:GetService("ServerScriptService").Server.Services.DataService)
				
				-- 檢查 DataService 是否有必要的方法
				expect(DataService.GetPlayerData).to.be.ok()
				expect(typeof(DataService.GetPlayerData)).to.equal("function")
				
				expect(DataService.UpdatePlayerData).to.be.ok()
				expect(typeof(DataService.UpdatePlayerData)).to.equal("function")
				
				expect(DataService.AddCoins).to.be.ok()
				expect(typeof(DataService.AddCoins)).to.equal("function")
			end)
		end)
		
	end)
end
