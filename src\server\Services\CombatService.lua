--[[
	CombatService - 戰鬥服務
	處理玩家、寵物和怪物之間的戰鬥邏輯
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")
local PetConfig = require(game:GetService("ReplicatedStorage").Shared.PetConfig)
local Components = require(game:GetService("ReplicatedStorage").Shared.ECS.Components)

local CombatService = Knit.CreateService({
	Name = "CombatService",
	Client = {
		AttackResult = Knit.CreateSignal(),
		TakeDamage = Knit.CreateSignal(),
		CombatUpdate = Knit.CreateSignal(),
	}
})

-- 私有變量
local combatStats = {} -- 玩家戰鬥狀態

function CombatService:KnitStart()
	print("⚔️ CombatService started")

	-- 監聽玩家加入
	Players.PlayerAdded:Connect(function(player)
		self:_initializePlayerCombat(player)
	end)

	-- 監聽玩家離開
	Players.PlayerRemoving:Connect(function(player)
		combatStats[player] = nil
	end)
end

function CombatService:KnitInit()
	-- 獲取其他服務
	self.PetService = Knit.GetService("PetService")
	self.DataService = Knit.GetService("DataService")
	self.WeaponService = Knit.GetService("WeaponService")
	self.ZoneService = Knit.GetService("ZoneService")
	self.EntityService = Knit.GetService("EntityService")
end

-- 初始化玩家戰鬥狀態
function CombatService:_initializePlayerCombat(player)
	combatStats[player] = {
		maxHealth = 100,
		currentHealth = 100,
		attack = 20,
		defense = 5,
		lastAttack = 0,
		attackCooldown = 1.5
	}
end

-- 客戶端請求攻擊怪物
function CombatService.Client:AttackMonster(player, instanceId)
	local currentTime = tick()
	local playerStats = combatStats[player]

	if not playerStats then
		CombatService:_initializePlayerCombat(player)
		playerStats = combatStats[player]
	end

	-- 檢查攻擊冷卻
	if currentTime - playerStats.lastAttack < playerStats.attackCooldown then
		return
	end

	-- 檢查距離
	if not CombatService:_isInAttackRange(player, instanceId) then
		return
	end

	-- 執行攻擊
	local damage = playerStats.attack
	local MonsterSpawnService = Knit.GetService("MonsterSpawnService")

	if MonsterSpawnService then
		local killed = self:_damageMonster(instanceId, damage, player)
		playerStats.lastAttack = currentTime

		-- 通知客戶端攻擊成功
		self.AttackResult:Fire(player, instanceId, damage, killed)

		print("⚔️ Player", player.Name, "attacked monster", instanceId, "for", damage, "damage")
	end
end

-- 客戶端請求寵物攻擊怪物
function CombatService.Client:PetAttackMonster(player, petId, instanceId)
	return self.Server:PetAttackMonster(player, petId, instanceId)
end

-- 移除重複的函數，邏輯已移到客戶端方法中

-- 檢查是否在攻擊範圍內
function CombatService:_isInAttackRange(player, instanceId)
	if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
		return false
	end

	local MonsterSpawnService = Knit.GetService("MonsterSpawnService")
	if not MonsterSpawnService then return false end

	local monsters = MonsterSpawnService:GetAllSpawnedMonsters()
	local monsterData = monsters[instanceId]

	if not monsterData then
		print("❌ Monster data not found for instanceId:", instanceId)
		return false
	end

	-- 查找怪物模型
	local monsterModel = workspace:FindFirstChild(monsterData.monsterId .. "_" .. instanceId)
	if not monsterModel or not monsterModel:FindFirstChild("HumanoidRootPart") then
		print("❌ Monster model not found:", monsterData.monsterId .. "_" .. instanceId)
		return false
	end

	local playerPos = player.Character.HumanoidRootPart.Position
	local monsterPos = monsterModel.HumanoidRootPart.Position
	local distance = (playerPos - monsterPos).Magnitude

	print("🎯 Attack range check: distance =", distance, "max = 10")
	return distance <= 10 -- 玩家攻擊範圍10格
end

-- 傷害怪物
function CombatService:_damageMonster(instanceId, damage, attacker)
	local MonsterSpawnService = Knit.GetService("MonsterSpawnService")
	if not MonsterSpawnService then return false end

	local monsters = MonsterSpawnService:GetAllSpawnedMonsters()
	local monsterData = monsters[instanceId]

	if not monsterData then
		print("❌ Monster not found for damage:", instanceId)
		return false
	end

	-- 查找怪物模型
	local monsterModel = workspace:FindFirstChild(monsterData.monsterId .. "_" .. instanceId)
	if not monsterModel then
		print("❌ Monster model not found for damage:", monsterData.monsterId .. "_" .. instanceId)
		return false
	end

	-- 計算傷害
	local currentHealth = monsterData.currentHealth or monsterData.config.health
	local newHealth = math.max(0, currentHealth - damage)
	monsterData.currentHealth = newHealth

	print("💥 Monster", instanceId, "took", damage, "damage. Health:", newHealth .. "/" .. monsterData.config.health)

	-- 更新血條顯示
	local body = monsterModel:FindFirstChild("Body")
	local healthDisplay = body and body:FindFirstChild("HealthDisplay")
	local healthBarBg = healthDisplay and healthDisplay:FindFirstChild("HealthBarBackground")
	local healthBar = healthBarBg and healthBarBg:FindFirstChild("HealthBar")
	local healthText = healthBarBg and healthBarBg:FindFirstChild("HealthText")

	if healthBar and healthText then
		local healthPercent = newHealth / monsterData.config.health
		healthBar.Size = UDim2.new(healthPercent, 0, 1, 0)

		-- 更新血條顏色
		if healthPercent > 0.6 then
			healthBar.BackgroundColor3 = Color3.new(0, 1, 0) -- 綠色
		elseif healthPercent > 0.3 then
			healthBar.BackgroundColor3 = Color3.new(1, 1, 0) -- 黃色
		else
			healthBar.BackgroundColor3 = Color3.new(1, 0, 0) -- 紅色
		end

		-- 更新血量文字
		healthText.Text = math.floor(newHealth) .. "/" .. monsterData.config.health

		print("🩸 Updated health bar:", math.floor(newHealth) .. "/" .. monsterData.config.health)
	else
		warn("❌ Health bar UI not found in monster:", monsterData.monsterId .. "_" .. instanceId)
	end

	-- 檢查是否死亡
	if newHealth <= 0 then
		print("💀 Monster", instanceId, "died")
		-- 銷毀模型
		monsterModel:Destroy()
		-- 清理數據
		monsters[instanceId] = nil
		return true -- 怪物死亡
	end

	return false -- 怪物未死亡
end

-- 處理傷害
function CombatService:DealDamage(target, damage, source, sourceId)
	if target:IsA("Player") then
		self:_damagePlayer(target, damage, source, sourceId)
	end
end

-- 玩家受到傷害
function CombatService:_damagePlayer(player, damage, source, sourceId)
	local playerStats = combatStats[player]
	if not playerStats then
		self:_initializePlayerCombat(player)
		playerStats = combatStats[player]
	end
	
	-- 計算實際傷害（考慮防禦力）
	local actualDamage = math.max(1, damage - playerStats.defense)
	playerStats.currentHealth = math.max(0, playerStats.currentHealth - actualDamage)
	
	-- 通知客戶端
	self.Client.TakeDamage:Fire(player, actualDamage, source, sourceId)
	self.Client.CombatUpdate:Fire(player, playerStats.currentHealth, playerStats.maxHealth)
	
	-- 檢查是否死亡
	if playerStats.currentHealth <= 0 then
		self:_handlePlayerDeath(player)
	end
	
	print("⚔️ Player", player.Name, "took", actualDamage, "damage from", source)
end

-- 處理玩家死亡
function CombatService:_handlePlayerDeath(player)
	-- 復活玩家（簡單實現）
	wait(2)
	local playerStats = combatStats[player]
	if playerStats then
		playerStats.currentHealth = playerStats.maxHealth
		self.Client.CombatUpdate:Fire(player, playerStats.currentHealth, playerStats.maxHealth)
	end
	
	-- 傳送到安全位置
	if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
		player.Character.HumanoidRootPart.CFrame = CFrame.new(0, 10, 0)
	end
	
	print("💀 Player", player.Name, "died and respawned")
end

-- 寵物攻擊怪物
function CombatService:PetAttackMonster(player, petId, instanceId)
	local petConfig = PetConfig.getPet(petId)
	if not petConfig then return end

	-- 檢查寵物是否在攻擊範圍內
	if not self:_isPetInAttackRange(player, petId, instanceId) then
		return
	end

	-- 計算寵物攻擊力
	local damage = petConfig.baseStats.attack or 15

	local MonsterService = Knit.GetService("MonsterService")
	if MonsterService then
		local killed = MonsterService:DamageMonster(instanceId, damage, player)

		-- 通知客戶端寵物攻擊
		self.Client.AttackResult:FireAll(instanceId, damage, killed, "pet", petId)

		print("🐾 Pet", petId, "attacked monster", instanceId, "for", damage, "damage")
	end
end

-- 檢查寵物是否在攻擊範圍內
function CombatService:_isPetInAttackRange(player, petId, instanceId)
	-- 尋找寵物模型
	local petModel = nil
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and model:FindFirstChild("PetId") and model:FindFirstChild("Owner") then
			local petIdValue = model:FindFirstChild("PetId")
			local ownerValue = model:FindFirstChild("Owner")

			if petIdValue.Value == petId and ownerValue.Value == player then
				petModel = model
				break
			end
		end
	end

	if not petModel or not petModel:FindFirstChild("HumanoidRootPart") then
		return false
	end

	-- 檢查怪物
	local MonsterService = Knit.GetService("MonsterService")
	if not MonsterService then return false end

	local monsters = MonsterService:GetActiveMonsters()
	local monsterData = monsters[instanceId]

	if not monsterData or not monsterData.model or not monsterData.model:FindFirstChild("HumanoidRootPart") then
		return false
	end

	local petPos = petModel.HumanoidRootPart.Position
	local monsterPos = monsterData.model.HumanoidRootPart.Position
	local distance = (petPos - monsterPos).Magnitude

	return distance <= 8 -- 寵物攻擊範圍8格
end

-- 獲取玩家戰鬥狀態
function CombatService:GetPlayerCombatStats(player)
	return combatStats[player]
end

-- 治療玩家
function CombatService:HealPlayer(player, amount)
	local playerStats = combatStats[player]
	if not playerStats then
		self:_initializePlayerCombat(player)
		playerStats = combatStats[player]
	end
	
	playerStats.currentHealth = math.min(playerStats.maxHealth, playerStats.currentHealth + amount)
	self.Client.CombatUpdate:Fire(player, playerStats.currentHealth, playerStats.maxHealth)
end

return CombatService
