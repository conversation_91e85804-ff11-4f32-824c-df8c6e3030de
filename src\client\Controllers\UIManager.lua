--[[
	UIManager - UI 生命週期管理器
	實現 Fusion UI 的 mount/unmount 控制
	根據遊戲狀態動態載入/卸載 UI
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")
local Fusion = require(game:GetService("ReplicatedStorage").Packages.fusion)

local UIManager = Knit.CreateController({
	Name = "UIManager",
})

-- Fusion 組件
local New = Fusion.New
local Value = Fusion.Value
local Computed = Fusion.Computed

-- 私有變量
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- UI 狀態管理
local gameState = Value("menu") -- menu, playing, paused, inventory
local mountedUIs = {} -- 已掛載的 UI 組件
local uiDefinitions = {} -- UI 定義

function UIManager:KnitStart()
	print("🎨 UIManager started")
	
	-- 監聽遊戲狀態變化
	gameState:observe(function()
		self:_updateUIState()
	end)
	
	-- 初始化 UI 定義
	self:_initializeUIDefinitions()
	
	-- 設置初始狀態
	self:SetGameState("playing")
end

function UIManager:KnitInit()
	-- 初始化控制器依賴
end

-- 初始化 UI 定義
function UIManager:_initializeUIDefinitions()
	uiDefinitions = {
		-- 戰鬥 UI
		combat = {
			states = {"playing"},
			priority = 1,
			factory = function()
				return self:_createCombatUI()
			end,
		},
		
		-- 寵物 UI
		petBook = {
			states = {"inventory"},
			priority = 2,
			factory = function()
				return self:_createPetBookUI()
			end,
		},
		
		-- 主選單 UI
		mainMenu = {
			states = {"menu"},
			priority = 3,
			factory = function()
				return self:_createMainMenuUI()
			end,
		},
		
		-- 暫停選單 UI
		pauseMenu = {
			states = {"paused"},
			priority = 4,
			factory = function()
				return self:_createPauseMenuUI()
			end,
		},
	}
end

-- 設置遊戲狀態
function UIManager:SetGameState(newState)
	print("🎨 Setting game state to:", newState)
	gameState:set(newState)
end

-- 獲取當前遊戲狀態
function UIManager:GetGameState()
	return gameState:get()
end

-- 更新 UI 狀態
function UIManager:_updateUIState()
	local currentState = gameState:get()
	print("🎨 Updating UI state for:", currentState)
	
	-- 卸載不需要的 UI
	for uiName, uiComponent in pairs(mountedUIs) do
		local definition = uiDefinitions[uiName]
		if definition and not self:_shouldShowUI(definition, currentState) then
			self:_unmountUI(uiName)
		end
	end
	
	-- 掛載需要的 UI
	for uiName, definition in pairs(uiDefinitions) do
		if self:_shouldShowUI(definition, currentState) and not mountedUIs[uiName] then
			self:_mountUI(uiName)
		end
	end
end

-- 檢查是否應該顯示 UI
function UIManager:_shouldShowUI(definition, currentState)
	for _, state in ipairs(definition.states) do
		if state == currentState then
			return true
		end
	end
	return false
end

-- 掛載 UI
function UIManager:_mountUI(uiName)
	local definition = uiDefinitions[uiName]
	if not definition then
		warn("❌ UI definition not found:", uiName)
		return
	end
	
	print("🎨 Mounting UI:", uiName)
	
	local uiComponent = definition.factory()
	if uiComponent then
		mountedUIs[uiName] = uiComponent
		print("✅ UI mounted:", uiName)
	else
		warn("❌ Failed to create UI:", uiName)
	end
end

-- 卸載 UI
function UIManager:_unmountUI(uiName)
	local uiComponent = mountedUIs[uiName]
	if uiComponent then
		print("🎨 Unmounting UI:", uiName)
		
		-- 銷毀 UI 組件
		if uiComponent.Destroy then
			uiComponent:Destroy()
		elseif typeof(uiComponent) == "Instance" then
			uiComponent:Destroy()
		end
		
		mountedUIs[uiName] = nil
		print("✅ UI unmounted:", uiName)
	end
end

-- 強制重新載入所有 UI
function UIManager:RefreshAllUI()
	print("🎨 Refreshing all UI")
	
	-- 卸載所有 UI
	for uiName in pairs(mountedUIs) do
		self:_unmountUI(uiName)
	end
	
	-- 重新載入
	self:_updateUIState()
end

-- 切換到庫存狀態
function UIManager:ShowInventory()
	self:SetGameState("inventory")
end

-- 切換到遊戲狀態
function UIManager:ShowGame()
	self:SetGameState("playing")
end

-- 切換到暫停狀態
function UIManager:ShowPause()
	self:SetGameState("paused")
end

-- 切換到主選單
function UIManager:ShowMainMenu()
	self:SetGameState("menu")
end

-- 檢查 UI 是否已掛載
function UIManager:IsUIMounted(uiName)
	return mountedUIs[uiName] ~= nil
end

-- 獲取已掛載的 UI
function UIManager:GetMountedUI(uiName)
	return mountedUIs[uiName]
end

-- 創建戰鬥 UI（從 CombatController 移過來）
function UIManager:_createCombatUI()
	-- 需要從 CombatController 獲取狀態
	local CombatController = Knit.GetController("CombatController")
	if not CombatController then
		warn("❌ CombatController not found")
		return nil
	end

	print("✅ CombatController found in UIManager")

	-- 將 CombatController 存儲為局部變量供事件使用
	local combatController = CombatController

	-- 獲取戰鬥狀態
	local healthState = CombatController:GetHealthState()
	local maxHealthState = CombatController:GetMaxHealthState()
	local attackCooldownState = CombatController:GetAttackCooldownState()
	local isAttackOnCooldown = CombatController:GetIsAttackOnCooldown()

	return New "ScreenGui" {
		Name = "CombatUI",
		Parent = playerGui,

		[Fusion.Children] = {
			-- 玩家血量條容器
			New "Frame" {
				Name = "PlayerHealthContainer",
				Size = UDim2.new(0, 250, 0, 60),
				Position = UDim2.new(0, 20, 0, 20),
				BackgroundTransparency = 1,

				[Fusion.Children] = {
					-- 血量條標題
					New "TextLabel" {
						Name = "HealthTitle",
						Size = UDim2.new(1, 0, 0, 20),
						Position = UDim2.new(0, 0, 0, 0),
						BackgroundTransparency = 1,
						Text = "玩家血量",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
						TextXAlignment = Enum.TextXAlignment.Left,
					},

					-- 血量條背景
					New "Frame" {
						Name = "HealthBarBackground",
						Size = UDim2.new(1, 0, 0, 25),
						Position = UDim2.new(0, 0, 0, 25),
						BackgroundColor3 = Color3.fromRGB(40, 40, 40),
						BorderSizePixel = 2,
						BorderColor3 = Color3.fromRGB(200, 200, 200),

						[Fusion.Children] = {
							-- 血量條填充
							New "Frame" {
								Name = "HealthFill",
								Size = Computed(function()
									local current = healthState:get()
									local max = maxHealthState:get()
									local ratio = max > 0 and current / max or 0
									return UDim2.new(ratio, 0, 1, 0)
								end),
								Position = UDim2.new(0, 0, 0, 0),
								BackgroundColor3 = Computed(function()
									local current = healthState:get()
									local max = maxHealthState:get()
									local ratio = max > 0 and current / max or 0

									if ratio > 0.6 then
										return Color3.fromRGB(100, 255, 100) -- 綠色
									elseif ratio > 0.3 then
										return Color3.fromRGB(255, 255, 100) -- 黃色
									else
										return Color3.fromRGB(255, 100, 100) -- 紅色
									end
								end),
								BorderSizePixel = 0,
							},

							-- 血量文字
							New "TextLabel" {
								Name = "HealthText",
								Size = UDim2.new(1, 0, 1, 0),
								Position = UDim2.new(0, 0, 0, 0),
								BackgroundTransparency = 1,
								Text = Computed(function()
									local current = math.floor(healthState:get())
									local max = math.floor(maxHealthState:get())
									return current .. " / " .. max .. " HP"
								end),
								TextColor3 = Color3.fromRGB(255, 255, 255),
								TextScaled = true,
								Font = Enum.Font.GothamBold,
								TextStrokeTransparency = 0,
								TextStrokeColor3 = Color3.fromRGB(0, 0, 0),
							}
						}
					},

					-- 血量百分比
					New "TextLabel" {
						Name = "HealthPercentage",
						Size = UDim2.new(1, 0, 0, 15),
						Position = UDim2.new(0, 0, 0, 50),
						BackgroundTransparency = 1,
						Text = Computed(function()
							local current = healthState:get()
							local max = maxHealthState:get()
							local percentage = max > 0 and (current / max * 100) or 0
							return string.format("%.1f%%", percentage)
						end),
						TextColor3 = Computed(function()
							local current = healthState:get()
							local max = maxHealthState:get()
							local ratio = max > 0 and current / max or 0

							if ratio > 0.6 then
								return Color3.fromRGB(100, 255, 100)
							elseif ratio > 0.3 then
								return Color3.fromRGB(255, 255, 100)
							else
								return Color3.fromRGB(255, 100, 100)
							end
						end),
						TextScaled = true,
						Font = Enum.Font.Gotham,
						TextXAlignment = Enum.TextXAlignment.Left,
					}
				}
			},

			-- 右下角按鈕容器
			New "Frame" {
				Name = "ButtonContainer",
				Size = UDim2.new(0, 250, 0, 120),
				Position = UDim2.new(1, -270, 1, -140),
				BackgroundTransparency = 1,

				[Fusion.Children] = {
					-- 攻擊按鈕容器
					New "Frame" {
						Name = "AttackButtonContainer",
						Size = UDim2.new(0, 110, 0, 50),
						Position = UDim2.new(0, 0, 0, 0),
						BackgroundTransparency = 1,

						[Fusion.Children] = {
							-- 攻擊按鈕
							New "TextButton" {
								Name = "AttackButton",
								Size = UDim2.new(1, 0, 1, 0),
								Position = UDim2.new(0, 0, 0, 0),
								BackgroundColor3 = Computed(function()
									return isAttackOnCooldown:get() and Color3.fromRGB(150, 50, 50) or Color3.fromRGB(255, 100, 100)
								end),
								BorderSizePixel = 2,
								BorderColor3 = Color3.fromRGB(255, 255, 255),
								Text = Computed(function()
									return isAttackOnCooldown:get() and "⏳ 冷卻中" or "⚔️ 攻擊"
								end),
								TextColor3 = Computed(function()
									return isAttackOnCooldown:get() and Color3.fromRGB(200, 200, 200) or Color3.fromRGB(255, 255, 255)
								end),
								TextScaled = true,
								Font = Enum.Font.GothamBold,
								Active = Computed(function()
									return not isAttackOnCooldown:get()
								end),

								[Fusion.OnEvent "Activated"] = function()
									print("🔥 Attack button clicked!")
									if not isAttackOnCooldown:get() then
										print("🔥 Attack not on cooldown, calling PerformAttack")
										if combatController and combatController.PerformAttack then
											combatController:PerformAttack()
										else
											warn("❌ CombatController or PerformAttack method not available")
										end
									else
										print("⏳ Attack on cooldown")
									end
								end,
							},

							-- 冷卻進度條
							New "Frame" {
								Name = "CooldownOverlay",
								Size = Computed(function()
									local progress = attackCooldownState:get()
									return UDim2.new(1, 0, progress, 0)
								end),
								Position = UDim2.new(0, 0, 1, 0),
								AnchorPoint = Vector2.new(0, 1),
								BackgroundColor3 = Color3.fromRGB(0, 0, 0),
								BackgroundTransparency = 0.6,
								BorderSizePixel = 0,
								Visible = Computed(function()
									return isAttackOnCooldown:get()
								end)
							}
						}
					},

					-- 治療按鈕（測試用）
					New "TextButton" {
						Name = "HealButton",
						Size = UDim2.new(0, 80, 0, 35),
						Position = UDim2.new(0, 120, 0, 0),
						BackgroundColor3 = Color3.fromRGB(100, 255, 100),
						BorderSizePixel = 2,
						BorderColor3 = Color3.fromRGB(255, 255, 255),
						Text = "💚 治療",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
						Active = true,

						[Fusion.OnEvent "Activated"] = function()
							combatController:PerformHeal()
						end
					},

					-- 召喚怪物按鈕
					New "TextButton" {
						Name = "SpawnMonsterButton",
						Size = UDim2.new(0, 110, 0, 50),
						Position = UDim2.new(0, 130, 0, 0),
						BackgroundColor3 = Color3.fromRGB(100, 255, 100),
						BorderSizePixel = 2,
						BorderColor3 = Color3.fromRGB(255, 255, 255),
						Text = "👹 召喚怪物",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
						Active = true,

						[Fusion.OnEvent "Activated"] = function()
							combatController:SpawnMonster()
						end,
					},

					-- 暫停按鈕
					New "TextButton" {
						Name = "PauseButton",
						Size = UDim2.new(0, 80, 0, 35),
						Position = UDim2.new(0, 120, 0, 40),
						BackgroundColor3 = Color3.fromRGB(255, 255, 100),
						BorderSizePixel = 2,
						BorderColor3 = Color3.fromRGB(255, 255, 255),
						Text = "⏸️ 暫停",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
						Active = true,

						[Fusion.OnEvent "Activated"] = function()
							UIManager:ShowPause()
						end
					},
				}
			}
		}
	}
end

-- 創建寵物圖鑑 UI
function UIManager:_createPetBookUI()
	return New "ScreenGui" {
		Name = "PetBookUI",
		Parent = playerGui,
		
		[Fusion.Children] = {
			New "Frame" {
				Name = "PetBookFrame",
				Size = UDim2.new(0.8, 0, 0.8, 0),
				Position = UDim2.new(0.1, 0, 0.1, 0),
				BackgroundColor3 = Color3.fromRGB(50, 50, 50),
				BorderSizePixel = 2,
				BorderColor3 = Color3.fromRGB(255, 255, 255),
				
				[Fusion.Children] = {
					New "TextLabel" {
						Name = "Title",
						Size = UDim2.new(1, 0, 0.1, 0),
						Position = UDim2.new(0, 0, 0, 0),
						BackgroundTransparency = 1,
						Text = "寵物圖鑑",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
					},
					
					New "TextButton" {
						Name = "CloseButton",
						Size = UDim2.new(0.1, 0, 0.1, 0),
						Position = UDim2.new(0.9, 0, 0, 0),
						BackgroundColor3 = Color3.fromRGB(255, 100, 100),
						Text = "X",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
						
						[Fusion.OnEvent "Activated"] = function()
							UIManager:ShowGame()
						end,
					},
				}
			}
		}
	}
end

-- 創建主選單 UI
function UIManager:_createMainMenuUI()
	return New "ScreenGui" {
		Name = "MainMenuUI",
		Parent = playerGui,
		
		[Fusion.Children] = {
			New "Frame" {
				Name = "MainMenuFrame",
				Size = UDim2.new(1, 0, 1, 0),
				Position = UDim2.new(0, 0, 0, 0),
				BackgroundColor3 = Color3.fromRGB(20, 20, 20),
				
				[Fusion.Children] = {
					New "TextLabel" {
						Name = "Title",
						Size = UDim2.new(0.6, 0, 0.2, 0),
						Position = UDim2.new(0.2, 0, 0.2, 0),
						BackgroundTransparency = 1,
						Text = "寵物戰鬥遊戲",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
					},
					
					New "TextButton" {
						Name = "PlayButton",
						Size = UDim2.new(0.3, 0, 0.1, 0),
						Position = UDim2.new(0.35, 0, 0.5, 0),
						BackgroundColor3 = Color3.fromRGB(100, 255, 100),
						Text = "開始遊戲",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
						
						[Fusion.OnEvent "Activated"] = function()
							UIManager:ShowGame()
						end,
					},
				}
			}
		}
	}
end

-- 創建暫停選單 UI
function UIManager:_createPauseMenuUI()
	return New "ScreenGui" {
		Name = "PauseMenuUI",
		Parent = playerGui,
		
		[Fusion.Children] = {
			New "Frame" {
				Name = "PauseMenuFrame",
				Size = UDim2.new(0.4, 0, 0.6, 0),
				Position = UDim2.new(0.3, 0, 0.2, 0),
				BackgroundColor3 = Color3.fromRGB(40, 40, 40),
				BorderSizePixel = 2,
				BorderColor3 = Color3.fromRGB(255, 255, 255),
				
				[Fusion.Children] = {
					New "TextLabel" {
						Name = "Title",
						Size = UDim2.new(1, 0, 0.2, 0),
						Position = UDim2.new(0, 0, 0, 0),
						BackgroundTransparency = 1,
						Text = "暫停",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
					},
					
					New "TextButton" {
						Name = "ResumeButton",
						Size = UDim2.new(0.8, 0, 0.15, 0),
						Position = UDim2.new(0.1, 0, 0.3, 0),
						BackgroundColor3 = Color3.fromRGB(100, 255, 100),
						Text = "繼續遊戲",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
						
						[Fusion.OnEvent "Activated"] = function()
							UIManager:ShowGame()
						end,
					},
					
					New "TextButton" {
						Name = "InventoryButton",
						Size = UDim2.new(0.8, 0, 0.15, 0),
						Position = UDim2.new(0.1, 0, 0.5, 0),
						BackgroundColor3 = Color3.fromRGB(100, 100, 255),
						Text = "寵物圖鑑",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
						
						[Fusion.OnEvent "Activated"] = function()
							UIManager:ShowInventory()
						end,
					},
					
					New "TextButton" {
						Name = "MainMenuButton",
						Size = UDim2.new(0.8, 0, 0.15, 0),
						Position = UDim2.new(0.1, 0, 0.7, 0),
						BackgroundColor3 = Color3.fromRGB(255, 100, 100),
						Text = "主選單",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
						
						[Fusion.OnEvent "Activated"] = function()
							UIManager:ShowMainMenu()
						end,
					},
				}
			}
		}
	}
end

return UIManager
