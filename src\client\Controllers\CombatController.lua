--[[
	CombatController - 戰鬥控制器
	處理客戶端戰鬥UI和輸入
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")
local Fusion = require(game:GetService("ReplicatedStorage").Packages.fusion)
local TargetingSystem = require(game:GetService("ReplicatedStorage").Shared.TargetingSystem)
local EffectPool = require(game:GetService("ReplicatedStorage").Shared.EffectPool)

local CombatController = Knit.CreateController({
	Name = "CombatController",
})

-- Fusion 組件
local New = Fusion.New
local Value = Fusion.Value
local Computed = Fusion.Computed
local OnEvent = Fusion.OnEvent
local Children = Fusion.Children

-- 私有變量
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")
local camera = workspace.CurrentCamera

-- 狀態變量
local healthState = Value(100)
local maxHealthState = Value(100)
local attackCooldownState = Value(0) -- 攻擊冷卻進度 (0-1)
local isAttackOnCooldown = Value(false)

function CombatController:KnitStart()
	print("⚔️ CombatController started")

	-- 初始化實例變量
	self.targetMonster = nil
	self.lastFoundTarget = nil
	self.lastAttackTime = 0
	self.attackCooldown = 1.5 -- 1.5秒攻擊冷卻
	self.lastHealthRatio = 1.0 -- 上次血量比例
	self.lowHealthWarning = false -- 低血量警告狀態
	
	-- 等待並獲取服務
	Knit.OnStart():andThen(function()
		print("🔥 Knit started, getting services...")
		self.CombatService = Knit.GetService("CombatService")
		self.MonsterService = Knit.GetService("MonsterService")
		self.MonsterSpawnService = Knit.GetService("MonsterSpawnService")
		self.WeaponService = Knit.GetService("WeaponService")
		print("🔥 Services obtained:", self.CombatService ~= nil, self.MonsterService ~= nil, self.MonsterSpawnService ~= nil, self.WeaponService ~= nil)

		-- 監聽戰鬥事件（客戶端信號）
		self.CombatService.TakeDamage:Connect(function(damage, source, sourceId)
			self:_showDamageEffect(damage, source)
		end)

		self.CombatService.CombatUpdate:Connect(function(currentHealth, maxHealth)
			local oldHealth = healthState:get()
			healthState:set(currentHealth)
			maxHealthState:set(maxHealth)

			-- 處理血量變化效果
			self:_handleHealthChange(oldHealth, currentHealth, maxHealth)
		end)

		self.CombatService.AttackResult:Connect(function(monsterId, damage, killed)
			self:_showAttackEffect(monsterId, damage, killed)
		end)

		-- 監聽怪物事件（客戶端信號）
		self.MonsterService.MonsterSpawned:Connect(function(instanceId, monsterId, position)
			self:_onMonsterSpawned(instanceId, monsterId, position)
		end)

		self.MonsterService.MonsterDied:Connect(function(instanceId)
			self:_onMonsterDied(instanceId)
		end)

		self.MonsterService.MonsterAttack:Connect(function(instanceId, targetUserId)
			self:_onMonsterAttack(instanceId, targetUserId)
		end)

		-- 監聽武器事件
		self.WeaponService.WeaponCreated:Connect(function(weaponModel)
			self:_onWeaponCreated(weaponModel)
		end)

		print("🔥 All event connections established")
	end):catch(function(err)
		warn("❌ Failed to initialize CombatController services:", err)
	end)

	-- 初始化特效池
	EffectPool.warmUp()
	print("🔥 Effect pool initialized")

	-- 為玩家裝備劍
	self:_ensurePlayerSword()

	-- 監聽角色重生事件，重新裝備劍
	player.CharacterAdded:Connect(function(character)
		character:WaitForChild("HumanoidRootPart")
		wait(1) -- 等待角色完全載入
		self:_ensurePlayerSword()
		print("⚔️ Sword re-equipped after respawn")
	end)
	
	-- 移除快捷鍵，只使用UI按鈕
	
	-- 啟動自動戰鬥循環
	RunService.Heartbeat:Connect(function()
		self:_updateCombat()
	end)
end

-- 狀態訪問方法（供 UIManager 使用）
function CombatController:GetHealthState()
	return healthState
end

function CombatController:GetMaxHealthState()
	return maxHealthState
end

function CombatController:GetAttackCooldownState()
	return attackCooldownState
end

function CombatController:GetIsAttackOnCooldown()
	return isAttackOnCooldown
end

-- 公開方法供 UI 調用
function CombatController:PerformAttack()
	print("🔥 PerformAttack called from UI")
	self:_performAttack()
end

function CombatController:PerformHeal()
	self:_performHeal()
end

function CombatController:SpawnMonster()
	self:_spawnMonster()
end

-- 移除了快捷鍵輸入，只使用UI按鈕操作

-- 執行攻擊
function CombatController:_performAttack()
	print("🔥 _performAttack called")

	-- 檢查冷卻時間
	local currentTime = tick()
	if currentTime - self.lastAttackTime < self.attackCooldown then
		print("❌ Attack on cooldown")
		return
	end

	if not self.CombatService then
		self:_showMessage("戰鬥服務未就緒！", Color3.fromRGB(255, 255, 0))
		print("❌ CombatService not available")
		return
	end

	print("🔥 Current targetMonster:", self.targetMonster)
	if not self.targetMonster then
		-- 尋找最近的怪物
		self.targetMonster = self:_findNearestMonster()
		print("🔥 Found new target:", self.targetMonster)
	end

	if self.targetMonster then
		print("🎯 Attacking monster:", self.targetMonster)

		-- 開始冷卻
		self:_startAttackCooldown()

		-- 播放攻擊動畫
		self:_playPlayerAttackAnimation()

		-- 發送攻擊到服務器
		self.CombatService.AttackMonster:Fire(self.targetMonster)
	else
		self:_showMessage("沒有目標！", Color3.fromRGB(255, 255, 0))
		print("❌ No target found")
	end
end

-- 執行治療（測試功能）
function CombatController:_performHeal()
	print("💚 _performHeal called")

	local currentHealth = healthState:get()
	local maxHealth = maxHealthState:get()

	-- 檢查是否需要治療
	if currentHealth >= maxHealth then
		self:_showMessage("血量已滿！", Color3.fromRGB(100, 255, 100))
		return
	end

	-- 計算治療量（25%最大血量）
	local healAmount = math.floor(maxHealth * 0.25)
	local newHealth = math.min(maxHealth, currentHealth + healAmount)

	-- 更新血量（模擬治療）
	healthState:set(newHealth)

	-- 觸發治療效果
	self:_handleHealthChange(currentHealth, newHealth, maxHealth)

	-- 顯示治療消息
	self:_showMessage("治療 +" .. healAmount, Color3.fromRGB(100, 255, 100))

	print("💚 Healed for", healAmount, "HP. New health:", newHealth .. "/" .. maxHealth)
end

-- 召喚怪物
function CombatController:_spawnMonster()
	if not self.MonsterSpawnService then
		self:_showMessage("怪物生成服務未就緒！", Color3.fromRGB(255, 255, 0))
		return
	end

	if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
		self:_showMessage("角色未就緒！", Color3.fromRGB(255, 255, 0))
		return
	end

	local playerPos = player.Character.HumanoidRootPart.Position
	local spawnPos = playerPos + Vector3.new(math.random(-10, 10), 0, math.random(-10, 10))

	print("🔥 Force spawning monster at:", spawnPos)
	self.MonsterSpawnService.ForceSpawnMonster:Fire(spawnPos)
	self:_showMessage("正在生成怪物...", Color3.fromRGB(0, 255, 0))
end

-- 尋找最近的怪物（優化版本）
function CombatController:_findNearestMonster()
	if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
		print("❌ No character or HumanoidRootPart")
		return nil
	end

	local playerPos = player.Character.HumanoidRootPart.Position
	print("🔍 Searching for monsters from position:", playerPos)

	-- 檢查場景中的怪物
	local monsterCount = 0
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and model:FindFirstChild("MonsterId") then
			monsterCount = monsterCount + 1
			print("🔍 Found monster:", model.Name, "at", model:FindFirstChild("HumanoidRootPart") and model.HumanoidRootPart.Position or "no position")
		end
	end
	print("🔍 Total monsters in workspace:", monsterCount)

	local nearestMonster, distance = TargetingSystem.findNearestMonster(playerPos, 30) -- 30格搜尋範圍

	print("🔍 Search result:", nearestMonster, "distance:", distance)

	-- 只在目標改變時輸出調試信息（僅開發模式）
	if nearestMonster ~= self.lastFoundTarget then
		if game:GetService("RunService"):IsStudio() then
			if nearestMonster then
				print("🎯 New target selected:", nearestMonster, "distance:", math.floor(distance or 0))
			else
				print("🎯 No target found")
			end
		end
		self.lastFoundTarget = nearestMonster
	end
	return nearestMonster
end

-- 開始攻擊冷卻
function CombatController:_startAttackCooldown()
	self.lastAttackTime = tick()
	isAttackOnCooldown:set(true)

	-- 啟動冷卻進度更新
	task.spawn(function()
		local startTime = self.lastAttackTime
		while tick() - startTime < self.attackCooldown do
			local elapsed = tick() - startTime
			local progress = 0.5 - (elapsed / self.attackCooldown)
			attackCooldownState:set(math.max(0, progress))
			task.wait(0.05) -- 20fps更新頻率
		end

		-- 冷卻結束
		isAttackOnCooldown:set(false)
		attackCooldownState:set(0)

		-- 播放冷卻完成音效
		self:_playSound("cooldown_ready")
	end)
end

-- 處理血量變化
function CombatController:_handleHealthChange(oldHealth, newHealth, maxHealth)
	local oldRatio = maxHealth > 0 and oldHealth / maxHealth or 0
	local newRatio = maxHealth > 0 and newHealth / maxHealth or 0

	-- 血量降低效果
	if newHealth < oldHealth then
		self:_playHealthDecreaseEffect(oldHealth - newHealth)
	end

	-- 血量恢復效果
	if newHealth > oldHealth then
		self:_playHealthIncreaseEffect(newHealth - oldHealth)
	end

	-- 低血量警告
	if newRatio <= 0.25 and not self.lowHealthWarning then
		self:_startLowHealthWarning()
	elseif newRatio > 0.25 and self.lowHealthWarning then
		self:_stopLowHealthWarning()
	end

	self.lastHealthRatio = newRatio
end

-- 播放血量降低效果
function CombatController:_playHealthDecreaseEffect(damage)
	-- 血量條震動
	local combatUI = playerGui:FindFirstChild("CombatUI")
	local healthContainer = combatUI and combatUI:FindFirstChild("PlayerHealthContainer")
	if healthContainer then
		local TweenService = game:GetService("TweenService")
		local originalPosition = healthContainer.Position

		-- 震動效果
		for _ = 1, 2 do
			local shake = TweenService:Create(
				healthContainer,
				TweenInfo.new(0.05, Enum.EasingStyle.Quad),
				{Position = originalPosition + UDim2.new(0, math.random(-3, 3), 0, math.random(-2, 2))}
			)
			shake:Play()
			shake.Completed:Wait()
		end

		-- 恢復原位
		healthContainer.Position = originalPosition
	end

	-- 顯示傷害數字
	self:_showPlayerDamageNumber(damage)
end

-- 播放血量恢復效果
function CombatController:_playHealthIncreaseEffect(healing)
	-- 綠色閃爍效果
	local combatUI = playerGui:FindFirstChild("CombatUI")
	local healthFill = combatUI and combatUI:FindFirstChild("PlayerHealthContainer") and
					   combatUI.PlayerHealthContainer:FindFirstChild("HealthBarBackground") and
					   combatUI.PlayerHealthContainer.HealthBarBackground:FindFirstChild("HealthFill")

	if healthFill then
		local TweenService = game:GetService("TweenService")
		local originalColor = healthFill.BackgroundColor3

		-- 綠色閃爍
		healthFill.BackgroundColor3 = Color3.fromRGB(150, 255, 150)
		local colorTween = TweenService:Create(
			healthFill,
			TweenInfo.new(0.5, Enum.EasingStyle.Quad),
			{BackgroundColor3 = originalColor}
		)
		colorTween:Play()
	end

	-- 顯示治療數字
	self:_showPlayerHealingNumber(healing)
end

-- 開始低血量警告
function CombatController:_startLowHealthWarning()
	self.lowHealthWarning = true

	-- 播放警告音效
	self:_playSound("low_health")

	-- 血量條閃爍效果
	local combatUI = playerGui:FindFirstChild("CombatUI")
	local healthContainer = combatUI and combatUI:FindFirstChild("PlayerHealthContainer")
	if healthContainer then
		task.spawn(function()
			local TweenService = game:GetService("TweenService")

			while self.lowHealthWarning do
				-- 紅色閃爍
				local flashTween = TweenService:Create(
					healthContainer,
					TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut),
					{BackgroundTransparency = 0.3}
				)
				flashTween:Play()
				flashTween.Completed:Wait()

				if not self.lowHealthWarning then break end

				local fadeTween = TweenService:Create(
					healthContainer,
					TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut),
					{BackgroundTransparency = 1}
				)
				fadeTween:Play()
				fadeTween.Completed:Wait()
			end
		end)
	end

	-- 只在開發模式下輸出警告信息
	if game:GetService("RunService"):IsStudio() then
		print("⚠️ Low health warning activated")
	end
end

-- 停止低血量警告
function CombatController:_stopLowHealthWarning()
	self.lowHealthWarning = false

	-- 恢復正常透明度
	local combatUI = playerGui:FindFirstChild("CombatUI")
	local healthContainer = combatUI and combatUI:FindFirstChild("PlayerHealthContainer")
	if healthContainer then
		healthContainer.BackgroundTransparency = 1
	end

	-- 只在開發模式下輸出警告信息
	if game:GetService("RunService"):IsStudio() then
		print("✅ Low health warning deactivated")
	end
end

-- 更新戰鬥邏輯（優化版本）
function CombatController:_updateCombat()
	-- 自動選擇目標
	if not self.targetMonster then
		self.targetMonster = self:_findNearestMonster()
	end

	-- 檢查目標是否仍然存在（使用優化的驗證）
	if self.targetMonster and not TargetingSystem.isMonsterValid(self.targetMonster) then
		self.targetMonster = nil
	end
end

-- 怪物生成事件
function CombatController:_onMonsterSpawned(instanceId, monsterId, position)
	self:_showMessage("怪物出現！", Color3.fromRGB(255, 100, 100))
	-- 播放召喚音效
	self:_playSound("spawn")
end

-- 怪物死亡事件
function CombatController:_onMonsterDied(instanceId)
	if self.targetMonster and self.targetMonster == instanceId then
		self.targetMonster = nil
	end
	self:_showMessage("怪物被擊敗！", Color3.fromRGB(100, 255, 100))
end

-- 怪物攻擊事件
function CombatController:_onMonsterAttack(instanceId, targetUserId)
	-- 只處理攻擊本地玩家的情況
	if targetUserId == player.UserId then
		-- 播放怪物攻擊動畫
		self:_playMonsterAttackAnimation(instanceId)
	end
end

-- 顯示傷害效果
function CombatController:_showDamageEffect(damage, source)
	self:_showMessage("-" .. damage, Color3.fromRGB(255, 100, 100))

	-- 紅色受傷畫面效果
	self:_showDamageScreen()

	-- 玩家受傷動畫
	self:_playPlayerHurtAnimation()

	-- 播放受傷音效
	self:_playSound("hurt")
end

-- 顯示攻擊效果
function CombatController:_showAttackEffect(monsterId, damage, killed)
	if killed then
		self:_showMessage("擊殺！+" .. damage, Color3.fromRGB(255, 255, 100))
		-- 擊殺特效
		self:_showKillEffect()
		-- 播放擊殺音效
		self:_playSound("kill")
	else
		self:_showMessage("+" .. damage, Color3.fromRGB(100, 255, 100))
	end

	-- 在怪物位置顯示傷害數字
	self:_showDamageNumber(monsterId, damage, killed)

	-- 玩家攻擊動畫
	self:_playPlayerAttackAnimation()

	-- 怪物受擊效果
	self:_showMonsterHitEffect(monsterId)

	-- 播放攻擊音效
	self:_playSound("attack")
end

-- 顯示玩家傷害數字
function CombatController:_showPlayerDamageNumber(damage)
	if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
		return
	end

	local damageGui = EffectPool.getEffect(EffectPool.TYPES.DAMAGE_NUMBER)
	if damageGui then
		damageGui.Adornee = player.Character.HumanoidRootPart
		damageGui.StudsOffset = Vector3.new(math.random(-1, 1), 2, math.random(-1, 1))
		damageGui.Parent = workspace

		local label = damageGui:FindFirstChild("TextLabel")
		if label then
			label.Text = "-" .. tostring(damage)
			label.TextColor3 = Color3.fromRGB(255, 50, 50) -- 紅色

			-- 動畫效果
			local TweenService = game:GetService("TweenService")
			local floatTween = TweenService:Create(
				damageGui,
				TweenInfo.new(1.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{StudsOffset = damageGui.StudsOffset + Vector3.new(0, 3, 0)}
			)

			local fadeTween = TweenService:Create(
				label,
				TweenInfo.new(1.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{TextTransparency = 1}
			)

			floatTween:Play()
			fadeTween:Play()

			-- 自動回收
			task.spawn(function()
				task.wait(1.5)
				EffectPool.returnEffect(damageGui)
			end)
		end
	end
end

-- 顯示玩家治療數字
function CombatController:_showPlayerHealingNumber(healing)
	if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
		return
	end

	local healingGui = EffectPool.getEffect(EffectPool.TYPES.DAMAGE_NUMBER)
	if healingGui then
		healingGui.Adornee = player.Character.HumanoidRootPart
		healingGui.StudsOffset = Vector3.new(math.random(-1, 1), 2, math.random(-1, 1))
		healingGui.Parent = workspace

		local label = healingGui:FindFirstChild("TextLabel")
		if label then
			label.Text = "+" .. tostring(healing)
			label.TextColor3 = Color3.fromRGB(100, 255, 100) -- 綠色

			-- 動畫效果
			local TweenService = game:GetService("TweenService")
			local floatTween = TweenService:Create(
				healingGui,
				TweenInfo.new(1.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{StudsOffset = healingGui.StudsOffset + Vector3.new(0, 3, 0)}
			)

			local fadeTween = TweenService:Create(
				label,
				TweenInfo.new(1.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{TextTransparency = 1}
			)

			floatTween:Play()
			fadeTween:Play()

			-- 自動回收
			task.spawn(function()
				task.wait(1.5)
				EffectPool.returnEffect(healingGui)
			end)
		end
	end
end

-- 顯示消息
function CombatController:_showMessage(text, color)
	local message = New "TextLabel" {
		Size = UDim2.new(0, 200, 0, 50),
		Position = UDim2.new(0.5, -100, 0.3, 0),
		BackgroundTransparency = 1,
		Text = text,
		TextColor3 = color,
		TextScaled = true,
		Font = Enum.Font.GothamBold,
		Parent = playerGui
	}

	-- 動畫效果
	local TweenService = game:GetService("TweenService")
	local tween = TweenService:Create(
		message,
		TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{
			Position = UDim2.new(0.5, -100, 0.2, 0),
			TextTransparency = 1
		}
	)

	tween:Play()
	tween.Completed:Connect(function()
		message:Destroy()
	end)
end

-- 紅色受傷畫面效果
function CombatController:_showDamageScreen()
	print("🔴 Creating damage screen overlay")
	local damageOverlay = New "Frame" {
		Name = "DamageOverlay",
		Size = UDim2.new(1, 0, 1, 0),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundColor3 = Color3.fromRGB(255, 0, 0),
		BackgroundTransparency = 0.3, -- 更不透明，更明顯
		BorderSizePixel = 0,
		ZIndex = 1000,
		Parent = playerGui
	}
	print("🔴 Damage overlay created:", damageOverlay.Name)

	-- 淡出動畫
	local TweenService = game:GetService("TweenService")
	local fadeOut = TweenService:Create(
		damageOverlay,
		TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{BackgroundTransparency = 1}
	)

	fadeOut:Play()
	fadeOut.Completed:Connect(function()
		damageOverlay:Destroy()
	end)
end

-- 玩家受傷動畫
function CombatController:_playPlayerHurtAnimation()
	if not player.Character or not player.Character:FindFirstChild("Humanoid") then
		return
	end

	local humanoid = player.Character.Humanoid
	local rootPart = player.Character:FindFirstChild("HumanoidRootPart")

	if rootPart then
		-- 震動效果
		local TweenService = game:GetService("TweenService")
		local originalCFrame = rootPart.CFrame

		for i = 1, 3 do
			local shake = TweenService:Create(
				rootPart,
				TweenInfo.new(0.05, Enum.EasingStyle.Quad),
				{CFrame = originalCFrame * CFrame.new(math.random(-1, 1), 0, math.random(-1, 1))}
			)
			shake:Play()
			shake.Completed:Wait()
		end

		-- 恢復原位
		rootPart.CFrame = originalCFrame
	end
end

-- 玩家攻擊動畫
function CombatController:_playPlayerAttackAnimation()
	print("🎬 Playing sword attack animation")
	if not player.Character or not player.Character:FindFirstChild("Humanoid") then
		print("❌ Player character or humanoid not found")
		return
	end

	local humanoid = player.Character.Humanoid
	local rootPart = player.Character:FindFirstChild("HumanoidRootPart")
	local rightArm = player.Character:FindFirstChild("Right Arm") or player.Character:FindFirstChild("RightUpperArm")

	if rootPart then
		print("🎬 Player rootPart found, starting sword attack")
		local TweenService = game:GetService("TweenService")

		-- 確保玩家有劍
		local sword = self:_ensurePlayerSword()

		-- 找到目標怪物的位置
		local targetPosition = nil
		if self.targetMonster then
			for _, model in pairs(workspace:GetChildren()) do
				if model:IsA("Model") and string.find(model.Name, self.targetMonster) and model:FindFirstChild("HumanoidRootPart") then
					targetPosition = model.HumanoidRootPart.Position
					break
				end
			end
		end

		if targetPosition then
			print("🎯 Target position found:", targetPosition)
			-- 轉向目標
			local direction = (targetPosition - rootPart.Position).Unit
			local lookAtCFrame = CFrame.lookAt(rootPart.Position, targetPosition)

			-- 轉向動畫
			local turnTween = TweenService:Create(
				rootPart,
				TweenInfo.new(0.1, Enum.EasingStyle.Quad),
				{CFrame = lookAtCFrame}
			)
			turnTween:Play()

			turnTween.Completed:Connect(function()
				-- 揮劍動畫
				self:_performSwordSwing(sword, targetPosition)
			end)
		else
			print("❌ No target position, using default")
			-- 沒有目標時的通用揮劍
			local defaultTarget = rootPart.Position + rootPart.CFrame.LookVector * 5
			self:_performSwordSwing(sword, defaultTarget)
		end
	end
end

-- 確保玩家有劍（通過 WeaponService）
function CombatController:_ensurePlayerSword()
	if not player.Character then return nil end

	-- 檢查是否已經有劍
	local existingSword = player.Character:FindFirstChild("PlayerSword")
	if existingSword then
		return existingSword
	end

	-- 通過 WeaponService 創建武器
	if self.WeaponService then
		self.WeaponService.EnsurePlayerWeapon:Fire()
	else
		warn("❌ WeaponService not available")
	end

	return nil
end

-- 武器創建完成事件處理
function CombatController:_onWeaponCreated(weaponModel)
	if weaponModel and weaponModel.Name then
		print("⚔️ Weapon created:", weaponModel.Name)
		-- 可以在這裡添加額外的客戶端武器設置邏輯
	else
		print("⚔️ Weapon created but model is nil or invalid")
	end
end

-- 執行揮劍動作（近距離劍砍版本）
function CombatController:_performSwordSwing(sword, targetPosition)
	print("🎬 _performSwordSwing called")
	if not player.Character then
		print("❌ No character")
		return
	end

	local playerPos = player.Character.HumanoidRootPart.Position
	local distance = (targetPosition - playerPos).Magnitude

	-- 檢查攻擊距離（近距離攻擊）
	if distance > 10 then
		print("❌ Target too far for sword attack:", distance)
		self:_showMessage("目標太遠！", Color3.fromRGB(255, 255, 0))
		return
	end

	print("⚔️ Performing close-range sword attack, distance:", distance)

	-- 近距離劍砍特效
	self:_createMeleeAttackEffects(sword, targetPosition)

	-- 創建斬擊痕跡
	self:_createSlashEffects(targetPosition)

	print("⚔️ Melee sword attack completed")
end

-- 創建劍光軌跡效果（近距離版本）
function CombatController:_createSwordTrailEffect(sword, targetPosition)
	if not sword or not sword:FindFirstChild("Handle") then
		print("❌ No sword or handle found for trail effect")
		return
	end

	local handle = sword:FindFirstChild("Handle")
	local playerPos = player.Character.HumanoidRootPart.Position
	local swordPos = handle.Position

	-- 創建劍光軌跡（短距離弧形）
	local trail = Instance.new("Part")
	trail.Name = "SwordTrail"
	trail.Size = Vector3.new(0.3, 0.1, 4) -- 較短的軌跡
	trail.Material = Enum.Material.Neon
	trail.BrickColor = BrickColor.new("Bright blue")
	trail.CanCollide = false
	trail.Anchored = true
	trail.Transparency = 0.2
	trail.Parent = workspace

	-- 劍光從劍的位置做弧形揮砍
	local startPos = swordPos + Vector3.new(0, 1, 0)
	local midPos = playerPos:Lerp(targetPosition, 0.7) + Vector3.new(0, 2, 0) -- 弧形中點
	local endPos = targetPosition + Vector3.new(0, 1, 0)

	-- 設置劍光軌跡的初始位置和方向
	local direction = (endPos - startPos).Unit
	local distance = (endPos - startPos).Magnitude
	trail.Size = Vector3.new(0.3, 0.1, distance)
	trail.CFrame = CFrame.lookAt(startPos, endPos)

	-- 劍光掃過動畫（快速揮砍效果）
	local TweenService = game:GetService("TweenService")

	-- 第一階段：劍光出現
	local appearTween = TweenService:Create(
		trail,
		TweenInfo.new(0.1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{
			Transparency = 0.1,
			Size = Vector3.new(0.5, 0.2, distance)
		}
	)

	appearTween:Play()
	appearTween.Completed:Connect(function()
		-- 第二階段：劍光消失
		local fadeTween = TweenService:Create(
			trail,
			TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.In),
			{
				Transparency = 1,
				Size = Vector3.new(0.1, 0.05, distance * 0.5)
			}
		)

		fadeTween:Play()
		fadeTween.Completed:Connect(function()
			trail:Destroy()
		end)
	end)

	print("✨ Melee sword trail effect created")
end

-- 創建斬擊特效（使用對象池）
function CombatController:_createSlashEffects(targetPosition)
	-- 爆炸效果
	EffectPool.playEffect(EffectPool.TYPES.EXPLOSION, targetPosition, {
		BlastRadius = 8,
		BlastPressure = 0
	}, 2)

	-- 斬擊痕跡
	EffectPool.playEffect(EffectPool.TYPES.SLASH_MARK, targetPosition, {
		CFrame = CFrame.new(targetPosition) * CFrame.Angles(0, 0, math.rad(45))
	}, 1)

	print("💥 Slash effects created at target (using pool)")
end

-- 近距離攻擊特效（劍砍版本）
function CombatController:_createMeleeAttackEffects(sword, targetPosition)
	print("✨ Creating melee attack effects")

	local playerPos = player.Character and player.Character.HumanoidRootPart.Position
	if not playerPos then return end

	-- 劍光軌跡（短距離）
	if sword then
		self:_createSwordTrailEffect(sword, targetPosition)
	end

	-- 在目標位置創建斬擊效果
	EffectPool.playEffect(EffectPool.TYPES.SLASH_MARK, targetPosition, {
		CFrame = CFrame.new(targetPosition) * CFrame.Angles(0, 0, math.rad(math.random(-45, 45)))
	}, 0.8)

	-- 小範圍爆炸效果
	EffectPool.playEffect(EffectPool.TYPES.EXPLOSION, targetPosition, {
		BlastRadius = 3,
		BlastPressure = 0
	}, 1)

	print("⚔️ Melee attack effects completed!")
end

-- 簡單的攻擊特效（遠距離版本，保留作為備用）
function CombatController:_createRangedAttackEffects(targetPosition)
	print("✨ Creating ranged attack effects (using pool)")

	-- 創建劍光從玩家位置射向目標
	local playerPos = player.Character and player.Character.HumanoidRootPart.Position
	if not playerPos then return end

	local distance = (targetPosition - playerPos).Magnitude
	local midPoint = playerPos:Lerp(targetPosition, 0.5) + Vector3.new(0, 1, 0)

	-- 劍光束
	EffectPool.playEffect(EffectPool.TYPES.SWORD_BEAM, midPoint, {
		Size = Vector3.new(0.5, 0.5, distance),
		CFrame = CFrame.lookAt(playerPos + Vector3.new(0, 1, 0), targetPosition)
	}, 0.5)

	-- 目標位置爆炸
	EffectPool.playEffect(EffectPool.TYPES.EXPLOSION, targetPosition, {
		BlastRadius = 8,
		BlastPressure = 0
	}, 2)

	-- 斬擊痕跡
	EffectPool.playEffect(EffectPool.TYPES.SLASH_MARK, targetPosition, {
		CFrame = CFrame.new(targetPosition) * CFrame.Angles(0, 0, math.rad(45))
	}, 1)

	print("⚔️ Ranged sword attack completed (using pool)!")
end

-- 擊殺特效
function CombatController:_showKillEffect()
	-- 創建擊殺特效
	local killEffect = New "Frame" {
		Name = "KillEffect",
		Size = UDim2.new(1, 0, 1, 0),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundColor3 = Color3.fromRGB(255, 215, 0),
		BackgroundTransparency = 0.8,
		BorderSizePixel = 0,
		ZIndex = 999,
		Parent = playerGui
	}

	local TweenService = game:GetService("TweenService")

	-- 閃爍效果
	for i = 1, 3 do
		local flash = TweenService:Create(
			killEffect,
			TweenInfo.new(0.1, Enum.EasingStyle.Quad),
			{BackgroundTransparency = 0.3}
		)
		flash:Play()
		flash.Completed:Wait()

		local fade = TweenService:Create(
			killEffect,
			TweenInfo.new(0.1, Enum.EasingStyle.Quad),
			{BackgroundTransparency = 0.8}
		)
		fade:Play()
		fade.Completed:Wait()
	end

	killEffect:Destroy()
end

-- 顯示傷害數字
function CombatController:_showDamageNumber(monsterId, damage, isKill)
	-- 尋找怪物模型
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and string.find(model.Name, monsterId) and model:FindFirstChild("HumanoidRootPart") then
			local rootPart = model.HumanoidRootPart

			-- 使用特效池創建傷害數字
			local damageGui = EffectPool.getEffect(EffectPool.TYPES.DAMAGE_NUMBER)
			if damageGui then
				damageGui.Adornee = rootPart
				damageGui.StudsOffset = Vector3.new(math.random(-1, 1), 3, math.random(-1, 1))
				damageGui.Parent = workspace

				local label = damageGui:FindFirstChild("TextLabel")
				if label then
					if isKill then
						label.Text = "KILL! " .. damage
						label.TextColor3 = Color3.fromRGB(255, 215, 0) -- 金色
					else
						label.Text = tostring(damage)
						label.TextColor3 = Color3.fromRGB(255, 100, 100) -- 紅色
					end

					-- 動畫效果
					local TweenService = game:GetService("TweenService")
					local floatTween = TweenService:Create(
						damageGui,
						TweenInfo.new(1.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
						{StudsOffset = damageGui.StudsOffset + Vector3.new(0, 3, 0)}
					)

					local fadeTween = TweenService:Create(
						label,
						TweenInfo.new(1.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
						{TextTransparency = 1}
					)

					floatTween:Play()
					fadeTween:Play()

					-- 自動回收
					task.spawn(function()
						task.wait(1.5)
						EffectPool.returnEffect(damageGui)
					end)
				end
			end
			break
		end
	end
end

-- 怪物受擊效果
function CombatController:_showMonsterHitEffect(monsterId)
	-- 尋找怪物模型
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and string.find(model.Name, monsterId) and model:FindFirstChild("HumanoidRootPart") then
			local rootPart = model.HumanoidRootPart

			-- 創建受擊特效
			EffectPool.playEffect(EffectPool.TYPES.EXPLOSION, rootPart.Position, {
				BlastRadius = 5,
				BlastPressure = 0
			}, 2)

			-- 怪物震動
			local TweenService = game:GetService("TweenService")
			local originalCFrame = rootPart.CFrame

			for _ = 1, 2 do
				local shake = TweenService:Create(
					rootPart,
					TweenInfo.new(0.05, Enum.EasingStyle.Quad),
					{CFrame = originalCFrame * CFrame.new(math.random(-0.5, 0.5), 0, math.random(-0.5, 0.5))}
				)
				shake:Play()
				shake.Completed:Wait()
			end

			rootPart.CFrame = originalCFrame
			break
		end
	end
end

-- 怪物攻擊動畫
function CombatController:_playMonsterAttackAnimation(instanceId)
	-- 尋找怪物模型
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and string.find(model.Name, instanceId) and model:FindFirstChild("HumanoidRootPart") then
			local rootPart = model.HumanoidRootPart
			local TweenService = game:GetService("TweenService")
			local originalCFrame = rootPart.CFrame

			-- 計算朝向玩家的方向
			if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
				local playerPos = player.Character.HumanoidRootPart.Position
				local direction = (playerPos - rootPart.Position).Unit
				local attackCFrame = rootPart.CFrame + direction * 2

				-- 攻擊衝刺
				local lunge = TweenService:Create(
					rootPart,
					TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
					{CFrame = attackCFrame}
				)

				lunge:Play()
				lunge.Completed:Connect(function()
					-- 回到原位
					local retreat = TweenService:Create(
						rootPart,
						TweenInfo.new(0.4, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
						{CFrame = originalCFrame}
					)
					retreat:Play()
				end)

				-- 創建攻擊特效
				local attackEffect = Instance.new("Explosion")
				attackEffect.Position = playerPos
				attackEffect.BlastRadius = 4
				attackEffect.BlastPressure = 0
				attackEffect.Parent = workspace
			end
			break
		end
	end
end

-- 播放音效
function CombatController:_playSound(soundType)
	-- 音效ID映射（這些是示例ID，實際使用時需要替換為真實的音效ID）
	local soundIds = {
		attack = "rbxasset://sounds/electronicpingshort.wav", -- 攻擊音效
		hurt = "rbxasset://sounds/impact_water.mp3", -- 受傷音效
		kill = "rbxasset://sounds/victory.wav", -- 擊殺音效
		spawn = "rbxasset://sounds/spawn.wav", -- 召喚音效
		cooldown_ready = "rbxasset://sounds/button.wav", -- 冷卻完成音效
		low_health = "rbxasset://sounds/alert.wav" -- 低血量警告音效
	}

	local soundId = soundIds[soundType]
	if soundId then
		local sound = Instance.new("Sound")
		sound.SoundId = soundId
		sound.Volume = 0.5
		sound.Parent = workspace
		sound:Play()

		-- 播放完畢後清理
		sound.Ended:Connect(function()
			sound:Destroy()
		end)
	end
end

return CombatController
