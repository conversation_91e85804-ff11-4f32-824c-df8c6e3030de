--[[
	AttackTest - 簡化的攻擊測試
	測試攻擊按鈕和相關功能
]]

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

local player = Players.LocalPlayer

-- 等待 Knit 啟動
Knit.OnStart():andThen(function()
	print("🧪 AttackTest: Knit started")
	
	-- 等待一下讓所有控制器初始化
	task.wait(3)
	
	-- 獲取控制器
	local CombatController = Knit.GetController("CombatController")
	local UIManager = Knit.GetController("UIManager")
	
	if not CombatController then
		warn("❌ CombatController not found")
		return
	end
	
	if not UIManager then
		warn("❌ UIManager not found")
		return
	end
	
	print("✅ Controllers found")
	
	-- 檢查戰鬥 UI
	local function checkCombatUI()
		print("🧪 Checking Combat UI...")

		local playerGui = player:WaitForChild("PlayerGui")
		local combatUI = playerGui:FindFirstChild("CombatUI")

		if combatUI then
			print("✅ CombatUI found")

			local attackButton = combatUI:FindFirstChild("ButtonContainer") and
								combatUI.ButtonContainer:FindFirstChild("AttackButtonContainer") and
								combatUI.ButtonContainer.AttackButtonContainer:FindFirstChild("AttackButton")

			if attackButton then
				print("✅ Attack button found:", attackButton.Name)
				print("✅ Attack button Active:", attackButton.Active)
				print("✅ Attack button Text:", attackButton.Text)
			else
				warn("❌ Attack button not found")
				print("🔍 ButtonContainer children:")
				if combatUI:FindFirstChild("ButtonContainer") then
					for _, child in pairs(combatUI.ButtonContainer:GetChildren()) do
						print("  -", child.Name, child.ClassName)
					end
				end
			end
		else
			warn("❌ CombatUI not found")
			print("🔍 PlayerGui children:")
			for _, child in pairs(playerGui:GetChildren()) do
				print("  -", child.Name, child.ClassName)
			end
		end
	end

	-- 檢查怪物血條
	local function checkMonsterHealthBars()
		print("🧪 Checking Monster Health Bars...")

		local monsterCount = 0
		for _, model in pairs(workspace:GetChildren()) do
			if model:IsA("Model") and model:FindFirstChild("MonsterId") then
				monsterCount = monsterCount + 1
				print("🔍 Found monster:", model.Name)

				local body = model:FindFirstChild("Body")
				local healthDisplay = body and body:FindFirstChild("HealthDisplay")

				if healthDisplay then
					print("✅ Health display found")

					local nameLabel = healthDisplay:FindFirstChild("NameLabel")
					local healthBarBg = healthDisplay:FindFirstChild("HealthBarBackground")
					local healthBar = healthBarBg and healthBarBg:FindFirstChild("HealthBar")
					local healthText = healthBarBg and healthBarBg:FindFirstChild("HealthText")

					if nameLabel then
						print("✅ Name label:", nameLabel.Text)
					end

					if healthBar and healthText then
						print("✅ Health bar found:", healthText.Text)
						print("✅ Health bar size:", healthBar.Size)
						print("✅ Health bar color:", healthBar.BackgroundColor3)
					else
						warn("❌ Health bar components missing")
					end
				else
					warn("❌ Health display not found for:", model.Name)
				end
			end
		end

		print("🔍 Total monsters with health bars:", monsterCount)
	end
	
	-- 測試怪物生成
	local function testMonsterSpawn()
		print("🧪 Testing monster spawn...")
		
		if CombatController.SpawnMonster then
			CombatController:SpawnMonster()
			print("✅ Monster spawn called")
		else
			warn("❌ SpawnMonster method not found")
		end
	end
	
	-- 測試攻擊功能
	local function testAttack()
		print("🧪 Testing attack...")

		if CombatController.PerformAttack then
			CombatController:PerformAttack()
			print("✅ Attack called")
		else
			warn("❌ PerformAttack method not found")
		end
	end

	-- 檢查 ECS 系統狀態
	local function checkECSStatus()
		print("🧪 Checking ECS Status...")

		-- 檢查全局 ECS World
		if _G.ECS_WORLD then
			print("✅ ECS World found")

			-- 嘗試查詢實體數量
			local playerCount = 0
			local monsterCount = 0

			-- 這裡我們無法直接查詢 ECS，但可以檢查怪物模型
			for _, model in pairs(workspace:GetChildren()) do
				if model:IsA("Model") then
					if model:FindFirstChild("MonsterId") then
						monsterCount = monsterCount + 1
						print("🔍 Found monster model:", model.Name)

						-- 檢查怪物是否在移動
						local humanoidRootPart = model:FindFirstChild("HumanoidRootPart")
						if humanoidRootPart then
							print("  Position:", humanoidRootPart.Position)
						end
					end
				end
			end

			print("🔍 Total monster models:", monsterCount)

			-- 檢查玩家角色
			if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
				print("✅ Player character found at:", player.Character.HumanoidRootPart.Position)
			else
				warn("❌ Player character not found")
			end
		else
			warn("❌ ECS World not found")
		end
	end
	
	-- 檢查 UIManager 狀態
	local function checkUIManager()
		print("🧪 Checking UIManager...")
		
		print("✅ UIManager game state:", UIManager:GetGameState())
		print("✅ UIManager mounted UIs:")
		
		-- 嘗試獲取已掛載的 UI（如果有公開方法）
		if UIManager.IsUIMounted then
			print("  - Combat UI mounted:", UIManager:IsUIMounted("combat"))
		end
	end
	
	-- 設置測試按鍵
	print("🧪 Setting up test keys...")
	print("📝 Test Controls:")
	print("  F1 - Check Combat UI")
	print("  F2 - Test Monster Spawn")
	print("  F3 - Test Attack")
	print("  F4 - Check UIManager")
	print("  F5 - Check Monster Health Bars")
	print("  F6 - Check ECS Status")
	print("  F7 - Run All Tests")
	
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end
		
		if input.KeyCode == Enum.KeyCode.F1 then
			checkCombatUI()
		elseif input.KeyCode == Enum.KeyCode.F2 then
			testMonsterSpawn()
		elseif input.KeyCode == Enum.KeyCode.F3 then
			testAttack()
		elseif input.KeyCode == Enum.KeyCode.F4 then
			checkUIManager()
		elseif input.KeyCode == Enum.KeyCode.F5 then
			checkMonsterHealthBars()
		elseif input.KeyCode == Enum.KeyCode.F6 then
			checkECSStatus()
		elseif input.KeyCode == Enum.KeyCode.F7 then
			print("🧪 Running all tests...")
			checkUIManager()
			task.wait(1)
			checkCombatUI()
			task.wait(1)
			checkMonsterHealthBars()
			task.wait(1)
			checkECSStatus()
			task.wait(1)
			testMonsterSpawn()
			task.wait(1)
			testAttack()
		end
	end)
	
	-- 自動運行初始測試
	task.wait(2)
	print("🧪 Running initial tests...")
	checkUIManager()
	task.wait(1)
	checkCombatUI()
	task.wait(1)
	print("🧪 Auto-spawning monster for testing...")
	testMonsterSpawn()
	
end):catch(function(err)
	warn("❌ AttackTest failed:", err)
end)
